{"name": "@arbiter/service-bot-detection", "version": "1.0.0", "description": "Arbiter Platform - Bot-detection Service", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0", "@tensorflow/tfjs-node": "^4.15.0", "redis": "^4.6.11", "axios": "^1.6.2"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1"}}