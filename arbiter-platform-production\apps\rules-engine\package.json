{"name": "@arbiter/rules-engine", "version": "1.0.0", "private": true, "main": "src/index.ts", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "type-check": "tsc --noEmit"}, "dependencies": {"@prisma/client": "^5.1.1", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.1", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "morgan": "^1.10.0", "nanoid": "^4.0.2", "redis": "^4.6.7", "zod": "^3.21.4"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.2", "@types/lodash": "^4.14.195", "@types/morgan": "^1.9.4", "@types/node": "^20.4.2", "jest": "^29.6.1", "tsx": "^3.12.7", "typescript": "^5.1.6"}}