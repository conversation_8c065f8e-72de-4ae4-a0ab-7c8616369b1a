# Security Policy

## Supported Versions

| Version | Supported          |
| ------- | ------------------ |
| 1.0.x   | :white_check_mark: |

## Reporting a Vulnerability

We take security seriously. If you discover a security vulnerability, please follow these steps:

1. **Do not** create a public GitHub issue
2. Email us <NAME_EMAIL>
3. Include detailed information about the vulnerability
4. Allow us 48 hours to respond
5. We will work with you to resolve the issue

## Security Measures

- All API communications use HTTPS/TLS encryption
- Input validation and sanitization on all user inputs
- SQL injection prevention through prepared statements
- CSRF protection on all forms
- Regular security audits and updates
- Secure API key management
- Rate limiting on API endpoints

## Responsible Disclosure

We appreciate security researchers who help keep our users safe. We will:

- Acknowledge your report within 48 hours
- Provide regular updates on our progress
- Credit you in our security acknowledgments (if desired)
- Work with you to understand and resolve the issue

Thank you for helping keep CrawlGuard secure!
