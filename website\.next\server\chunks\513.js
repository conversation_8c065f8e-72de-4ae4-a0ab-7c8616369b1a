exports.id=513,exports.ids=[513],exports.modules={43:(e,t,n)=>{"use strict";n.d(t,{jH:()=>a});var r=n(3210);n(687);var o=r.createContext(void 0);function a(e){let t=r.useContext(o);return e||t||"ltr"}},195:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return l},urlObjectKeys:function(){return i}});let r=n(740)._(n(6715)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:n}=e,a=e.protocol||"",i=e.pathname||"",l=e.hash||"",u=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:n&&(c=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(c+=":"+e.port)),u&&"object"==typeof u&&(u=String(r.urlQueryToSearchParams(u)));let s=e.search||u&&"?"+u||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==c?(c="//"+(c||""),i&&"/"!==i[0]&&(i="/"+i)):c||(c=""),l&&"#"!==l[0]&&(l="#"+l),s&&"?"!==s[0]&&(s="?"+s),""+a+c+(i=i.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+l}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return a(e)}},363:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},554:(e,t)=>{"use strict";function n(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return n}})},593:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return f},PrefetchPriority:function(){return d},cancelPrefetchTask:function(){return u},createCacheKey:function(){return s},getCurrentCacheVersion:function(){return i},navigate:function(){return o},prefetch:function(){return r},reschedulePrefetchTask:function(){return c},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return l}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,o=n,a=n,i=n,l=n,u=n,c=n,s=n;var f=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),d=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},642:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return s},extractPathFromFlightRouterState:function(){return c},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],a=Array.isArray(t),i=a?t[1]:t;!i||i.startsWith(o.PAGE_SEGMENT_KEY)||(a&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):a&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(2859),o=n(3913),a=n(4077),i=e=>"/"===e[0]?e.slice(1):e,l=e=>"string"==typeof e?"children"===e?"":e:e[1];function u(e){return e.reduce((e,t)=>""===(t=i(t))||(0,o.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function c(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===o.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(o.PAGE_SEGMENT_KEY))return"";let a=[l(n)],i=null!=(t=e[1])?t:{},s=i.children?c(i.children):void 0;if(void 0!==s)a.push(s);else for(let[e,t]of Object.entries(i)){if("children"===e)continue;let n=c(t);void 0!==n&&a.push(n)}return u(a)}function s(e,t){let n=function e(t,n){let[o,i]=t,[u,s]=n,f=l(o),d=l(u);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>f.startsWith(e)||d.startsWith(e)))return"";if(!(0,a.matchSegment)(o,u)){var p;return null!=(p=c(n))?p:""}for(let t in i)if(s[t]){let n=e(i[t],s[t]);if(null!==n)return l(u)+"/"+n}return null}(e,t);return null==n||"/"===n?n:u(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},660:(e,t)=>{"use strict";function n(e){let t=5381;for(let n=0;n<e.length;n++)t=(t<<5)+t+e.charCodeAt(n)|0;return t>>>0}function r(e){return n(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{djb2Hash:function(){return n},hexHash:function(){return r}})},1134:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(2688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},1359:(e,t,n)=>{"use strict";n.d(t,{Oh:()=>a});var r=n(3210),o=0;function a(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??i()),document.body.insertAdjacentElement("beforeend",e[1]??i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},1437:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return o},extractInterceptionRouteInformation:function(){return i},isInterceptionRouteAppPath:function(){return a}});let r=n(4722),o=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>o.find(t=>e.startsWith(t)))}function i(e){let t,n,a;for(let r of e.split("/"))if(n=o.find(e=>r.startsWith(e))){[t,a]=e.split(n,2);break}if(!t||!n||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,r.normalizeAppPath)(t),n){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let i=t.split("/");if(i.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=i.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},1500:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,a,i,l,u,c){if(0===Object.keys(i[1]).length){n.head=u;return}for(let s in i[1]){let f,d=i[1][s],p=d[0],h=(0,r.createRouterCacheKey)(p),g=null!==l&&void 0!==l[2][s]?l[2][s]:null;if(a){let r=a.parallelRoutes.get(s);if(r){let a,i=(null==c?void 0:c.kind)==="auto"&&c.status===o.PrefetchCacheEntryStatus.reusable,l=new Map(r),f=l.get(h);a=null!==g?{lazyData:null,rsc:g[1],prefetchRsc:null,head:null,prefetchHead:null,loading:g[3],parallelRoutes:new Map(null==f?void 0:f.parallelRoutes),navigatedAt:t}:i&&f?{lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==f?void 0:f.parallelRoutes),loading:null,navigatedAt:t},l.set(h,a),e(t,a,f,d,g||null,u,c),n.parallelRoutes.set(s,l);continue}}if(null!==g){let e=g[1],n=g[3];f={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else f={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let m=n.parallelRoutes.get(s);m?m.set(h,f):n.parallelRoutes.set(s,new Map([[h,f]])),e(t,f,void 0,d,g,u,c)}}}});let r=n(3123),o=n(9154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1550:(e,t)=>{"use strict";function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},1658:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillMetadataSegment:function(){return d},normalizeMetadataPageToRoute:function(){return h},normalizeMetadataRoute:function(){return p}});let r=n(8304),o=function(e){return e&&e.__esModule?e:{default:e}}(n(8671)),a=n(6341),i=n(4396),l=n(660),u=n(4722),c=n(2958),s=n(5499);function f(e){let t=o.default.dirname(e);if(e.endsWith("/sitemap"))return"";let n="";return t.split("/").some(e=>(0,s.isGroupSegment)(e)||(0,s.isParallelRouteSegment)(e))&&(n=(0,l.djb2Hash)(t).toString(36).slice(0,6)),n}function d(e,t,n){let r=(0,u.normalizeAppPath)(e),l=(0,i.getNamedRouteRegex)(r,{prefixRouteKeys:!1}),s=(0,a.interpolateDynamicPath)(r,t,l),{name:d,ext:p}=o.default.parse(n),h=f(o.default.posix.join(e,d)),g=h?`-${h}`:"";return(0,c.normalizePathSep)(o.default.join(s,`${d}${g}${p}`))}function p(e){if(!(0,r.isMetadataPage)(e))return e;let t=e,n="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":n=f(e),!t.endsWith("/route")){let{dir:e,name:r,ext:a}=o.default.parse(t);t=o.default.posix.join(e,`${r}${n?`-${n}`:""}${a}`,"route")}return t}function h(e,t){let n=e.endsWith("/route"),r=n?e.slice(0,-6):e,o=r.endsWith("/sitemap")?".xml":"";return(t?`${r}/[__metadata_id__]`:`${r}${o}`)+(n?"/route":"")}},1794:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let r=n(9289),o=n(6736);function a(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},2030:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],o=n[0];if(Array.isArray(r)&&Array.isArray(o)){if(r[0]!==o[0]||r[2]!==o[2])return!0}else if(r!==o)return!0;if(t[4])return!n[4];if(n[4])return!0;let a=Object.values(t[1])[0],i=Object.values(n[1])[0];return!a||!i||e(a,i)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2247:(e,t,n)=>{"use strict";n.d(t,{A:()=>G});var r,o,a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(3210)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var f="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,d=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return i.options=a({async:!0,ssr:!1},e),i}(),g=function(){},m=l.forwardRef(function(e,t){var n,r,o,u,c=l.useRef(null),p=l.useState({onScrollCapture:g,onWheelCapture:g,onTouchMoveCapture:g}),m=p[0],y=p[1],v=e.forwardProps,b=e.children,R=e.className,_=e.removeScrollBar,E=e.enabled,w=e.shards,P=e.sideCar,x=e.noRelative,T=e.noIsolation,O=e.inert,j=e.allowPinchZoom,S=e.as,M=e.gapMode,A=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,f(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}d.set(u,n)},[n]),u),N=a(a({},A),m);return l.createElement(l.Fragment,null,E&&l.createElement(P,{sideCar:h,removeScrollBar:_,shards:w,noRelative:x,noIsolation:T,inert:O,setCallbacks:y,allowPinchZoom:!!j,lockRef:c,gapMode:M}),v?l.cloneElement(l.Children.only(b),a(a({},N),{ref:C})):l.createElement(void 0===S?"div":S,a({},N,{className:R,ref:C}),b))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:c,zeroRight:u};var y=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,a({},n))};y.isSideCarExport=!0;var v=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=v();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},R=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},_={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},w=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},P=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return _;var t=w(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},x=R(),T="data-scroll-locked",O=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(T,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(T,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},j=function(){var e=parseInt(document.body.getAttribute(T)||"0",10);return isFinite(e)?e:0},S=function(){l.useEffect(function(){return document.body.setAttribute(T,(j()+1).toString()),function(){var e=j()-1;e<=0?document.body.removeAttribute(T):document.body.setAttribute(T,e.toString())}},[])},M=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;S();var a=l.useMemo(function(){return P(o)},[o]);return l.createElement(x,{styles:O(a,!t,o,n?"":"!important")})},A=!1;if("undefined"!=typeof window)try{var C=Object.defineProperty({},"passive",{get:function(){return A=!0,!0}});window.addEventListener("test",C,C),window.removeEventListener("test",C,C)}catch(e){A=!1}var N=!!A&&{passive:!1},L=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},k=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),D(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},D=function(e,t){return"v"===e?L(t,"overflowY"):L(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},U=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=i*r,u=n.target,c=t.contains(u),s=!1,f=l>0,d=0,p=0;do{if(!u)break;var h=I(e,u),g=h[0],m=h[1]-h[2]-i*g;(g||m)&&D(e,u)&&(d+=m,p+=g);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&l>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},K=function(e){return e&&"current"in e?e.current:e},z=0,B=[];let W=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(z++)[0],a=l.useState(R)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(K),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=F(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-a[0],c="deltaY"in e?e.deltaY:l[1]-a[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=k(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=k(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return U(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(B.length&&B[B.length-1]===a){var n="deltaY"in e?H(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(K).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),f=l.useCallback(function(e){n.current=F(e),r.current=void 0},[]),d=l.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,F(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return B.push(a),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",c,N),document.addEventListener("touchmove",c,N),document.addEventListener("touchstart",f,N),function(){B=B.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,N),document.removeEventListener("touchmove",c,N),document.removeEventListener("touchstart",f,N)}},[]);var h=e.removeScrollBar,g=e.inert;return l.createElement(l.Fragment,null,g?l.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(M,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),y);var $=l.forwardRef(function(e,t){return l.createElement(m,a({},e,{ref:t,sideCar:W}))});$.classNames=m.classNames;let G=$},2255:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let r=n(1550);function o(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},2308:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,o,,i]=t;for(let l in r.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==i&&(t[2]=n,t[3]="refresh"),o)e(o[l],n)}},refreshInactiveParallelSegments:function(){return i}});let r=n(6928),o=n(9008),a=n(3913);async function i(e){let t=new Set;await l({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function l(e){let{navigatedAt:t,state:n,updatedTree:a,updatedCache:i,includeNextUrl:u,fetchedSegments:c,rootTree:s=a,canonicalUrl:f}=e,[,d,p,h]=a,g=[];if(p&&p!==f&&"refresh"===h&&!c.has(p)){c.add(p);let e=(0,o.fetchServerResponse)(new URL(p,location.origin),{flightRouterState:[s[0],s[1],s[2],"refetch"],nextUrl:u?n.nextUrl:null}).then(e=>{let{flightData:n}=e;if("string"!=typeof n)for(let e of n)(0,r.applyFlightData)(t,i,i,e)});g.push(e)}for(let e in d){let r=l({navigatedAt:t,state:n,updatedTree:d[e],updatedCache:i,includeNextUrl:u,fetchedSegments:c,rootTree:s,canonicalUrl:f});g.push(r)}await Promise.all(g)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2437:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return o}});let r=n(5362);function o(e,t){let n=[],o=(0,r.pathToRegexp)(e,n,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,r.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(o.source),o.flags):o,n);return(e,r)=>{if("string"!=typeof e)return!1;let o=a(e);if(!o)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of n)"number"==typeof e.name&&delete o.params[e.name];return{...r,...o.params}}}},2547:(e,t,n)=>{"use strict";n.d(t,{n:()=>f});var r=n(3210),o=n(8599),a=n(4163),i=n(3495),l=n(687),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:m,onUnmountAutoFocus:y,...v}=e,[b,R]=r.useState(null),_=(0,i.c)(m),E=(0,i.c)(y),w=r.useRef(null),P=(0,o.s)(t,e=>R(e)),x=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(x.paused||!b)return;let t=e.target;b.contains(t)?w.current=t:h(w.current,{select:!0})},t=function(e){if(x.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||h(w.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,b,x.paused]),r.useEffect(()=>{if(b){g.add(x);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(u,s);b.addEventListener(u,_),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(d(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(b))}return()=>{b.removeEventListener(u,_),setTimeout(()=>{let t=new CustomEvent(c,s);b.addEventListener(c,E),b.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),b.removeEventListener(c,E),g.remove(x)},0)}}},[b,_,E,x]);let T=r.useCallback(e=>{if(!n&&!f||x.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(a,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,x.paused]);return(0,l.jsx)(a.sG.div,{tabIndex:-1,...v,ref:P,onKeyDown:T})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var g=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=m(e,t)).unshift(t)},remove(t){e=m(e,t),e[0]?.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},2708:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},2785:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},2942:(e,t,n)=>{"use strict";n.d(t,{RG:()=>_,bL:()=>M,q7:()=>A});var r=n(3210),o=n(569),a=n(9510),i=n(8599),l=n(1273),u=n(6963),c=n(4163),s=n(3495),f=n(5551),d=n(43),p=n(687),h="rovingFocusGroup.onEntryFocus",g={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[y,v,b]=(0,a.N)(m),[R,_]=(0,l.A)(m,[b]),[E,w]=R(m),P=r.forwardRef((e,t)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(x,{...e,ref:t})})}));P.displayName=m;var x=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:l=!1,dir:u,currentTabStopId:y,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:R,onEntryFocus:_,preventScrollOnEntryFocus:w=!1,...P}=e,x=r.useRef(null),T=(0,i.s)(t,x),O=(0,d.jH)(u),[j,M]=(0,f.i)({prop:y,defaultProp:b??null,onChange:R,caller:m}),[A,C]=r.useState(!1),N=(0,s.c)(_),L=v(n),k=r.useRef(!1),[D,I]=r.useState(0);return r.useEffect(()=>{let e=x.current;if(e)return e.addEventListener(h,N),()=>e.removeEventListener(h,N)},[N]),(0,p.jsx)(E,{scope:n,orientation:a,dir:O,loop:l,currentTabStopId:j,onItemFocus:r.useCallback(e=>M(e),[M]),onItemShiftTab:r.useCallback(()=>C(!0),[]),onFocusableItemAdd:r.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>I(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:A||0===D?-1:0,"data-orientation":a,...P,ref:T,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!k.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(h,g);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),w)}}k.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>C(!1))})})}),T="RovingFocusGroupItem",O=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:i=!1,tabStopId:l,children:s,...f}=e,d=(0,u.B)(),h=l||d,g=w(T,n),m=g.currentTabStopId===h,b=v(n),{onFocusableItemAdd:R,onFocusableItemRemove:_,currentTabStopId:E}=g;return r.useEffect(()=>{if(a)return R(),()=>_()},[a,R,_]),(0,p.jsx)(y.ItemSlot,{scope:n,id:h,focusable:a,active:i,children:(0,p.jsx)(c.sG.span,{tabIndex:m?0:-1,"data-orientation":g.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?g.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>g.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void g.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return j[o]}(e,g.orientation,g.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=g.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>S(n))}}),children:"function"==typeof s?s({isCurrentTabStop:m,hasTabStop:null!=E}):s})})});O.displayName=T;var j={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var M=P,A=O},2958:(e,t)=>{"use strict";function n(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return n}})},3038:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(3210);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=a(e,r)),t&&(o.current=a(t,r))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let n=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function o(e){return n.test(e)?e.replace(r,"\\$&"):e}},3376:(e,t,n)=>{"use strict";n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,i={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],f=[],d=new Set,p=new Set(c),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};c.forEach(h);var g=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))g(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,l),s.set(e,u),f.push(e),1===l&&i&&a.set(e,!0),1===u&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return g(t),d.clear(),l++,function(){f.forEach(function(e){var t=o.get(e)-1,i=s.get(e)-1;o.set(e,t),s.set(e,i),t||(a.has(e)||e.removeAttribute(r),a.delete(e)),i||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,a=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),c(o,a,n,"aria-hidden")):function(){return null}}},3406:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return c},PENDING_LINK_STATUS:function(){return u},mountFormInstance:function(){return v},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return R},onNavigationIntent:function(){return _},pingVisibleLinks:function(){return w},setLinkForCurrentNavigation:function(){return s},unmountLinkForCurrentNavigation:function(){return f},unmountPrefetchableInstance:function(){return b}}),n(3690);let r=n(9752),o=n(9154),a=n(593),i=n(3210),l=null,u={pending:!0},c={pending:!1};function s(e){(0,i.startTransition)(()=>{null==l||l.setOptimisticLinkStatus(c),null==e||e.setOptimisticLinkStatus(u),l=e})}function f(e){l===e&&(l=null)}let d="function"==typeof WeakMap?new WeakMap:new Map,p=new Set,h="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;R(t.target,e)}},{rootMargin:"200px"}):null;function g(e,t){void 0!==d.get(e)&&b(e),d.set(e,t),null!==h&&h.observe(e)}function m(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,n,r,o,a){if(o){let o=m(t);if(null!==o){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:a};return g(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function v(e,t,n,r){let o=m(t);null!==o&&g(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:o.href,setOptimisticLinkStatus:null})}function b(e){let t=d.get(e);if(void 0!==t){d.delete(e),p.delete(t);let n=t.prefetchTask;null!==n&&(0,a.cancelPrefetchTask)(n)}null!==h&&h.unobserve(e)}function R(e,t){let n=d.get(e);void 0!==n&&(n.isVisible=t,t?p.add(n):p.delete(n),E(n))}function _(e,t){let n=d.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,E(n))}function E(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function w(e,t){let n=(0,a.getCurrentCacheVersion)();for(let r of p){let i=r.prefetchTask;if(null!==i&&r.cacheVersion===n&&i.key.nextUrl===e&&i.treeAtTimeOfPrefetch===t)continue;null!==i&&(0,a.cancelPrefetchTask)(i);let l=(0,a.createCacheKey)(r.prefetchHref,e),u=r.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;r.prefetchTask=(0,a.schedulePrefetchTask)(l,t,r.kind===o.PrefetchKind.FULL,u),r.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3690:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return h},dispatchNavigateAction:function(){return y},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return g},publicAppRouterInstance:function(){return b}});let r=n(9154),o=n(8830),a=n(3210),i=n(1992);n(593);let l=n(9129),u=n(6127),c=n(9752),s=n(5076),f=n(3406);function d(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?p({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function p(e){let{actionQueue:t,action:n,setState:r}=e,o=t.state;t.pending=n;let a=n.payload,l=t.action(o,a);function u(e){n.discarded||(t.state=e,d(t,r),n.resolve(e))}(0,i.isThenable)(l)?l.then(u,e=>{d(t,r),n.reject(e)}):u(l)}function h(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let o={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{o={resolve:e,reject:t}});(0,a.startTransition)(()=>{n(e)})}let i={payload:t,next:null,resolve:o.resolve,reject:o.reject};null===e.pending?(e.last=i,p({actionQueue:e,action:i,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,i.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),p({actionQueue:e,action:i,setState:n})):(null!==e.last&&(e.last.next=i),e.last=i)})(n,e,t),action:async(e,t)=>(0,o.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return n}function g(){return null}function m(){return null}function y(e,t,n,o){let a=new URL((0,u.addBasePath)(e),location.href);(0,f.setLinkForCurrentNavigation)(o);(0,l.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:a,isExternalUrl:(0,c.isExternalURL)(a),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function v(e,t){(0,l.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),o=(0,c.createPrefetchURL)(e);if(null!==o){var a;(0,s.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:o,kind:null!=(a=null==t?void 0:t.kind)?a:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var n;y(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var n;y(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,l.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3736:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return o}}),n(4827);let r=n(2785);function o(e,t,n){void 0===n&&(n=!0);let o=new URL("http://n"),a=t?new URL(t,o):e.startsWith(".")?new URL("http://n"):o,{pathname:i,searchParams:l,search:u,hash:c,href:s,origin:f}=new URL(e,a);if(f!==o.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:i,query:n?(0,r.searchParamsToUrlQuery)(l):void 0,search:u,hash:c,href:s.slice(f.length)}}},3898:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return u},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return c}});let r=n(4400),o=n(1500),a=n(3123),i=n(3913);function l(e,t,n,l,u,c){let{segmentPath:s,seedData:f,tree:d,head:p}=l,h=t,g=n;for(let t=0;t<s.length;t+=2){let n=s[t],l=s[t+1],m=t===s.length-2,y=(0,a.createRouterCacheKey)(l),v=g.parallelRoutes.get(n);if(!v)continue;let b=h.parallelRoutes.get(n);b&&b!==v||(b=new Map(v),h.parallelRoutes.set(n,b));let R=v.get(y),_=b.get(y);if(m){if(f&&(!_||!_.lazyData||_===R)){let t=f[0],n=f[1],a=f[3];_={lazyData:null,rsc:c||t!==i.PAGE_SEGMENT_KEY?n:null,prefetchRsc:null,head:null,prefetchHead:null,loading:a,parallelRoutes:c&&R?new Map(R.parallelRoutes):new Map,navigatedAt:e},R&&c&&(0,r.invalidateCacheByRouterState)(_,R,d),c&&(0,o.fillLazyItemsTillLeafWithHead)(e,_,R,d,f,p,u),b.set(y,_)}continue}_&&R&&(_===R&&(_={lazyData:_.lazyData,rsc:_.rsc,prefetchRsc:_.prefetchRsc,head:_.head,prefetchHead:_.prefetchHead,parallelRoutes:new Map(_.parallelRoutes),loading:_.loading},b.set(y,_)),h=_,g=R)}}function u(e,t,n,r,o){l(e,t,n,r,o,!0)}function c(e,t,n,r,o){l(e,t,n,r,o,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3931:(e,t,n)=>{"use strict";n.d(t,{UC:()=>eG,q7:()=>eq,ZL:()=>e$,bL:()=>eB,l9:()=>eW});var r=n(3210),o=n(569),a=n(8599),i=n(1273),l=n(5551),u=n(4163),c=n(9510),s=n(43),f=n(1355),d=n(1359),p=n(2547),h=n(6963),g=n(8674),m=n(5028),y=n(6059),v=n(2942),b=n(8730),R=n(3495),_=n(3376),E=n(2247),w=n(687),P=["Enter"," "],x=["ArrowUp","PageDown","End"],T=["ArrowDown","PageUp","Home",...x],O={ltr:[...P,"ArrowRight"],rtl:[...P,"ArrowLeft"]},j={ltr:["ArrowLeft"],rtl:["ArrowRight"]},S="Menu",[M,A,C]=(0,c.N)(S),[N,L]=(0,i.A)(S,[C,g.Bk,v.RG]),k=(0,g.Bk)(),D=(0,v.RG)(),[I,U]=N(S),[F,H]=N(S),K=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:a,onOpenChange:i,modal:l=!0}=e,u=k(t),[c,f]=r.useState(null),d=r.useRef(!1),p=(0,R.c)(i),h=(0,s.jH)(a);return r.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,w.jsx)(g.bL,{...u,children:(0,w.jsx)(I,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:f,children:(0,w.jsx)(F,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:d,dir:h,modal:l,children:o})})})};K.displayName=S;var z=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=k(n);return(0,w.jsx)(g.Mz,{...o,...r,ref:t})});z.displayName="MenuAnchor";var B="MenuPortal",[W,$]=N(B,{forceMount:void 0}),G=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=U(B,t);return(0,w.jsx)(W,{scope:t,forceMount:n,children:(0,w.jsx)(y.C,{present:n||a.open,children:(0,w.jsx)(m.Z,{asChild:!0,container:o,children:r})})})};G.displayName=B;var q="MenuContent",[X,V]=N(q),Y=r.forwardRef((e,t)=>{let n=$(q,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=U(q,e.__scopeMenu),i=H(q,e.__scopeMenu);return(0,w.jsx)(M.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(y.C,{present:r||a.open,children:(0,w.jsx)(M.Slot,{scope:e.__scopeMenu,children:i.modal?(0,w.jsx)(Q,{...o,ref:t}):(0,w.jsx)(Z,{...o,ref:t})})})})}),Q=r.forwardRef((e,t)=>{let n=U(q,e.__scopeMenu),i=r.useRef(null),l=(0,a.s)(t,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,_.Eq)(e)},[]),(0,w.jsx)(ee,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Z=r.forwardRef((e,t)=>{let n=U(q,e.__scopeMenu);return(0,w.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),J=(0,b.TL)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:i=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:m,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:R,onDismiss:_,disableOutsideScroll:P,...O}=e,j=U(q,n),S=H(q,n),M=k(n),C=D(n),N=A(n),[L,I]=r.useState(null),F=r.useRef(null),K=(0,a.s)(t,F,j.onContentChange),z=r.useRef(0),B=r.useRef(""),W=r.useRef(0),$=r.useRef(null),G=r.useRef("right"),V=r.useRef(0),Y=P?E.A:r.Fragment,Q=e=>{let t=B.current+e,n=N().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,a=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,i=(r=Math.max(a,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(n.map(e=>e.textValue),t,o),i=n.find(e=>e.textValue===a)?.ref.current;!function e(t){B.current=t,window.clearTimeout(z.current),""!==t&&(z.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};r.useEffect(()=>()=>window.clearTimeout(z.current),[]),(0,d.Oh)();let Z=r.useCallback(e=>G.current===$.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],l=t[a],u=i.x,c=i.y,s=l.x,f=l.y;c>r!=f>r&&n<(s-u)*(r-c)/(f-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,$.current?.area),[]);return(0,w.jsx)(X,{scope:n,searchRef:B,onItemEnter:r.useCallback(e=>{Z(e)&&e.preventDefault()},[Z]),onItemLeave:r.useCallback(e=>{Z(e)||(F.current?.focus(),I(null))},[Z]),onTriggerLeave:r.useCallback(e=>{Z(e)&&e.preventDefault()},[Z]),pointerGraceTimerRef:W,onPointerGraceIntentChange:r.useCallback(e=>{$.current=e},[]),children:(0,w.jsx)(Y,{...P?{as:J,allowPinchZoom:!0}:void 0,children:(0,w.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),F.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,w.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:m,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:R,onDismiss:_,children:(0,w.jsx)(v.bL,{asChild:!0,...C,dir:S.dir,orientation:"vertical",loop:i,currentTabStopId:L,onCurrentTabStopIdChange:I,onEntryFocus:(0,o.m)(h,e=>{S.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,w.jsx)(g.UC,{role:"menu","aria-orientation":"vertical","data-state":eT(j.open),"data-radix-menu-content":"",dir:S.dir,...M,...O,ref:K,style:{outline:"none",...O.style},onKeyDown:(0,o.m)(O.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Q(e.key));let o=F.current;if(e.target!==o||!T.includes(e.key))return;e.preventDefault();let a=N().filter(e=>!e.disabled).map(e=>e.ref.current);x.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(z.current),B.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eS(e=>{let t=e.target,n=V.current!==e.clientX;e.currentTarget.contains(t)&&n&&(G.current=e.clientX>V.current?"right":"left",V.current=e.clientX)}))})})})})})})});Y.displayName=q;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",eo="menu.itemSelect",ea=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:i,...l}=e,c=r.useRef(null),s=H(er,e.__scopeMenu),f=V(er,e.__scopeMenu),d=(0,a.s)(t,c),p=r.useRef(!1);return(0,w.jsx)(ei,{...l,ref:d,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>i?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==f.searchRef.current;n||t&&" "===e.key||P.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=er;var ei=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:i=!1,textValue:l,...c}=e,s=V(er,n),f=D(n),d=r.useRef(null),p=(0,a.s)(t,d),[h,g]=r.useState(!1),[m,y]=r.useState("");return r.useEffect(()=>{let e=d.current;e&&y((e.textContent??"").trim())},[c.children]),(0,w.jsx)(M.ItemSlot,{scope:n,disabled:i,textValue:l??m,children:(0,w.jsx)(v.q7,{asChild:!0,...f,focusable:!i,children:(0,w.jsx)(u.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eS(e=>{i?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eS(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>g(!0)),onBlur:(0,o.m)(e.onBlur,()=>g(!1))})})})}),el=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...a}=e;return(0,w.jsx)(eg,{scope:e.__scopeMenu,checked:n,children:(0,w.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eO(n)?"mixed":n,...a,ref:t,"data-state":ej(n),onSelect:(0,o.m)(a.onSelect,()=>r?.(!!eO(n)||!n),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[ec,es]=N(eu,{value:void 0,onValueChange:()=>{}}),ef=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,a=(0,R.c)(r);return(0,w.jsx)(ec,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,w.jsx)(et,{...o,ref:t})})});ef.displayName=eu;var ed="MenuRadioItem",ep=r.forwardRef((e,t)=>{let{value:n,...r}=e,a=es(ed,e.__scopeMenu),i=n===a.value;return(0,w.jsx)(eg,{scope:e.__scopeMenu,checked:i,children:(0,w.jsx)(ea,{role:"menuitemradio","aria-checked":i,...r,ref:t,"data-state":ej(i),onSelect:(0,o.m)(r.onSelect,()=>a.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ep.displayName=ed;var eh="MenuItemIndicator",[eg,em]=N(eh,{checked:!1}),ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,a=em(eh,n);return(0,w.jsx)(y.C,{present:r||eO(a.checked)||!0===a.checked,children:(0,w.jsx)(u.sG.span,{...o,ref:t,"data-state":ej(a.checked)})})});ey.displayName=eh;var ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,w.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ev.displayName="MenuSeparator";var eb=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=k(n);return(0,w.jsx)(g.i3,{...o,...r,ref:t})});eb.displayName="MenuArrow";var[eR,e_]=N("MenuSub"),eE="MenuSubTrigger",ew=r.forwardRef((e,t)=>{let n=U(eE,e.__scopeMenu),i=H(eE,e.__scopeMenu),l=e_(eE,e.__scopeMenu),u=V(eE,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:f}=u,d={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),f(null)}},[s,f]),(0,w.jsx)(z,{asChild:!0,...d,children:(0,w.jsx)(ei,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eT(n.open),...e,ref:(0,a.t)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eS(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eS(e=>{p();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,a=t[o?"left":"right"],i=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:a,y:t.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;e.disabled||r&&" "===t.key||O[i.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});ew.displayName=eE;var eP="MenuSubContent",ex=r.forwardRef((e,t)=>{let n=$(q,e.__scopeMenu),{forceMount:i=n.forceMount,...l}=e,u=U(q,e.__scopeMenu),c=H(q,e.__scopeMenu),s=e_(eP,e.__scopeMenu),f=r.useRef(null),d=(0,a.s)(t,f);return(0,w.jsx)(M.Provider,{scope:e.__scopeMenu,children:(0,w.jsx)(y.C,{present:i||u.open,children:(0,w.jsx)(M.Slot,{scope:e.__scopeMenu,children:(0,w.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:d,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&f.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=j[c.dir].includes(e.key);t&&n&&(u.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eT(e){return e?"open":"closed"}function eO(e){return"indeterminate"===e}function ej(e){return eO(e)?"indeterminate":e?"checked":"unchecked"}function eS(e){return t=>"mouse"===t.pointerType?e(t):void 0}ex.displayName=eP;var eM="DropdownMenu",[eA,eC]=(0,i.A)(eM,[L]),eN=L(),[eL,ek]=eA(eM),eD=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:a,defaultOpen:i,onOpenChange:u,modal:c=!0}=e,s=eN(t),f=r.useRef(null),[d,p]=(0,l.i)({prop:a,defaultProp:i??!1,onChange:u,caller:eM});return(0,w.jsx)(eL,{scope:t,triggerId:(0,h.B)(),triggerRef:f,contentId:(0,h.B)(),open:d,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,w.jsx)(K,{...s,open:d,onOpenChange:p,dir:o,modal:c,children:n})})};eD.displayName=eM;var eI="DropdownMenuTrigger",eU=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...i}=e,l=ek(eI,n),c=eN(n);return(0,w.jsx)(z,{asChild:!0,...c,children:(0,w.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...i,ref:(0,a.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eU.displayName=eI;var eF=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eN(t);return(0,w.jsx)(G,{...r,...n})};eF.displayName="DropdownMenuPortal";var eH="DropdownMenuContent",eK=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=ek(eH,n),l=eN(n),u=r.useRef(!1);return(0,w.jsx)(Y,{id:i.contentId,"aria-labelledby":i.triggerId,...l,...a,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||i.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!i.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eK.displayName=eH,r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,w.jsx)(et,{...o,...r,ref:t})}).displayName="DropdownMenuGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,w.jsx)(en,{...o,...r,ref:t})}).displayName="DropdownMenuLabel";var ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,w.jsx)(ea,{...o,...r,ref:t})});ez.displayName="DropdownMenuItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,w.jsx)(el,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,w.jsx)(ef,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,w.jsx)(ep,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,w.jsx)(ey,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,w.jsx)(ev,{...o,...r,ref:t})}).displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,w.jsx)(eb,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,w.jsx)(ew,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eN(n);return(0,w.jsx)(ex,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eB=eD,eW=eU,e$=eF,eG=eK,eq=ez},4396:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getNamedMiddlewareRegex:function(){return g},getNamedRouteRegex:function(){return h},getRouteRegex:function(){return f},parseParameter:function(){return u}});let r=n(6143),o=n(1437),a=n(3293),i=n(2887),l=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function u(e){let t=e.match(l);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let n=e.startsWith("...");return n&&(e=e.slice(3)),{key:e,repeat:n,optional:t}}function s(e,t,n){let r={},u=1,s=[];for(let f of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.find(e=>f.startsWith(e)),i=f.match(l);if(e&&i&&i[2]){let{key:t,optional:n,repeat:o}=c(i[2]);r[t]={pos:u++,repeat:o,optional:n},s.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(i&&i[2]){let{key:e,repeat:t,optional:o}=c(i[2]);r[e]={pos:u++,repeat:t,optional:o},n&&i[1]&&s.push("/"+(0,a.escapeStringRegexp)(i[1]));let l=t?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";n&&i[1]&&(l=l.substring(1)),s.push(l)}else s.push("/"+(0,a.escapeStringRegexp)(f));t&&i&&i[3]&&s.push((0,a.escapeStringRegexp)(i[3]))}return{parameterizedRoute:s.join(""),groups:r}}function f(e,t){let{includeSuffix:n=!1,includePrefix:r=!1,excludeOptionalTrailingSlash:o=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:i}=s(e,n,r),l=a;return o||(l+="(?:/)?"),{re:RegExp("^"+l+"$"),groups:i}}function d(e){let t,{interceptionMarker:n,getSafeRouteKey:r,segment:o,routeKeys:i,keyPrefix:l,backreferenceDuplicateKeys:u}=e,{key:s,optional:f,repeat:d}=c(o),p=s.replace(/\W/g,"");l&&(p=""+l+p);let h=!1;(0===p.length||p.length>30)&&(h=!0),isNaN(parseInt(p.slice(0,1)))||(h=!0),h&&(p=r());let g=p in i;l?i[p]=""+l+s:i[p]=s;let m=n?(0,a.escapeStringRegexp)(n):"";return t=g&&u?"\\k<"+p+">":d?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",f?"(?:/"+m+t+")?":"/"+m+t}function p(e,t,n,u,c){let s,f=(s=0,()=>{let e="",t=++s;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},h=[];for(let s of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.some(e=>s.startsWith(e)),i=s.match(l);if(e&&i&&i[2])h.push(d({getSafeRouteKey:f,interceptionMarker:i[1],segment:i[2],routeKeys:p,keyPrefix:t?r.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(i&&i[2]){u&&i[1]&&h.push("/"+(0,a.escapeStringRegexp)(i[1]));let e=d({getSafeRouteKey:f,segment:i[2],routeKeys:p,keyPrefix:t?r.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});u&&i[1]&&(e=e.substring(1)),h.push(e)}else h.push("/"+(0,a.escapeStringRegexp)(s));n&&i&&i[3]&&h.push((0,a.escapeStringRegexp)(i[3]))}return{namedParameterizedRoute:h.join(""),routeKeys:p}}function h(e,t){var n,r,o;let a=p(e,t.prefixRouteKeys,null!=(n=t.includeSuffix)&&n,null!=(r=t.includePrefix)&&r,null!=(o=t.backreferenceDuplicateKeys)&&o),i=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(i+="(?:/)?"),{...f(e,t),namedRegex:"^"+i+"$",routeKeys:a.routeKeys}}function g(e,t){let{parameterizedRoute:n}=s(e,!1,!1),{catchAll:r=!0}=t;if("/"===n)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:o}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+o+(r?"(?:(/.*)?)":"")+"$"}}},4397:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return o}});let r=n(3123);function o(e,t){return function e(t,n,o){if(0===Object.keys(n).length)return[t,o];let a=Object.keys(n).filter(e=>"children"!==e);for(let i of("children"in n&&a.unshift("children"),a)){let[a,l]=n[i],u=t.parallelRoutes.get(i);if(!u)continue;let c=(0,r.createRouterCacheKey)(a),s=u.get(c);if(!s)continue;let f=e(s,l,o+"/"+c);if(f)return f}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4400:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return o}});let r=n(3123);function o(e,t,n){for(let o in n[1]){let a=n[1][o][0],i=(0,r.createRouterCacheKey)(a),l=t.parallelRoutes.get(o);if(l){let t=new Map(l);t.delete(i),e.parallelRoutes.set(o,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4642:(e,t)=>{"use strict";function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},4674:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let r=n(4949),o=n(1550),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:a}=(0,o.parsePath)(e);return/\.[^/]+\/?$/.test(t)?""+(0,r.removeTrailingSlash)(t)+n+a:t.endsWith("/")?""+t+n+a:t+"/"+n+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4722:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return i}});let r=n(5531),o=n(5499);function a(e){return(0,r.ensureLeadingSlash)(e.split("/").reduce((e,t,n,r)=>!t||(0,o.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&n===r.length-1?e:e+"/"+t,""))}function i(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return g},PageNotFoundError:function(){return m},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return u},getLocationOrigin:function(){return i},getURL:function(){return l},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function i(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function l(){let{href:e}=window.location,t=i();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&c(n))return r;if(!r)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class g extends Error{}class m extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},4949:(e,t)=>{"use strict";function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},5076:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return i}});let r=n(5144),o=n(5334),a=new r.PromiseQueue(5),i=function(e,t){(0,o.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,o.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5144:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return c}});let r=n(6312),o=n(9656);var a=o._("_maxConcurrency"),i=o._("_runningCount"),l=o._("_queue"),u=o._("_processNext");class c{enqueue(e){let t,n,o=new Promise((e,r)=>{t=e,n=r}),a=async()=>{try{r._(this,i)[i]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,i)[i]--,r._(this,u)[u]()}};return r._(this,l)[l].push({promiseFn:o,task:a}),r._(this,u)[u](),o}bump(e){let t=r._(this,l)[l].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,l)[l].splice(t,1)[0];r._(this,l)[l].unshift(e),r._(this,u)[u](!0)}}constructor(e=5){Object.defineProperty(this,u,{value:s}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,i,{writable:!0,value:void 0}),Object.defineProperty(this,l,{writable:!0,value:void 0}),r._(this,a)[a]=e,r._(this,i)[i]=0,r._(this,l)[l]=[]}}function s(e){if(void 0===e&&(e=!1),(r._(this,i)[i]<r._(this,a)[a]||e)&&r._(this,l)[l].length>0){var t;null==(t=r._(this,l)[l].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5232:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,n){let{url:_,isExternalUrl:E,navigateType:w,shouldScroll:P,allowAliasing:x}=n,T={},{hash:O}=_,j=(0,o.createHrefFromUrl)(_),S="push"===w;if((0,m.prunePrefetchCache)(t.prefetchCache),T.preserveCustomHistoryState=!1,T.pendingPush=S,E)return b(t,T,_.toString(),S);if(document.getElementById("__next-page-redirect"))return b(t,T,j,S);let M=(0,m.getOrCreatePrefetchCacheEntry)({url:_,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:x}),{treeAtTimeOfPrefetch:A,data:C}=M;return d.prefetchQueue.bump(C),C.then(d=>{let{flightData:m,canonicalUrl:E,postponed:w}=d,x=Date.now(),C=!1;if(M.lastUsedTime||(M.lastUsedTime=x,C=!0),M.aliased){let r=(0,v.handleAliasedPrefetchEntry)(x,t,m,_,T);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof m)return b(t,T,m,S);let N=E?(0,o.createHrefFromUrl)(E):j;if(O&&t.canonicalUrl.split("#",1)[0]===N.split("#",1)[0])return T.onlyHashChange=!0,T.canonicalUrl=N,T.shouldScroll=P,T.hashFragment=O,T.scrollableSegments=[],(0,s.handleMutable)(t,T);let L=t.tree,k=t.cache,D=[];for(let e of m){let{pathToSegment:n,seedData:o,head:s,isHeadPartial:d,isRootRender:m}=e,v=e.tree,E=["",...n],P=(0,i.applyRouterStatePatchToTree)(E,L,v,j);if(null===P&&(P=(0,i.applyRouterStatePatchToTree)(E,A,v,j)),null!==P){if(o&&m&&w){let e=(0,g.startPPRNavigation)(x,k,L,v,o,s,d,!1,D);if(null!==e){if(null===e.route)return b(t,T,j,S);P=e.route;let n=e.node;null!==n&&(T.cache=n);let o=e.dynamicRequestTree;if(null!==o){let n=(0,r.fetchServerResponse)(_,{flightRouterState:o,nextUrl:t.nextUrl});(0,g.listenForDynamicRequest)(e,n)}}else P=v}else{if((0,u.isNavigatingToNewRootLayout)(L,P))return b(t,T,j,S);let r=(0,p.createEmptyCacheNode)(),o=!1;for(let t of(M.status!==c.PrefetchCacheEntryStatus.stale||C?o=(0,f.applyFlightData)(x,k,r,e,M):(o=function(e,t,n,r){let o=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),R(r).map(e=>[...n,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,a),o=!0;return o}(r,k,n,v),M.lastUsedTime=x),(0,l.shouldHardNavigate)(E,L)?(r.rsc=k.rsc,r.prefetchRsc=k.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(r,k,n),T.cache=r):o&&(T.cache=r,k=r),R(v))){let e=[...n,...t];e[e.length-1]!==h.DEFAULT_SEGMENT_KEY&&D.push(e)}}L=P}}return T.patchedTree=L,T.canonicalUrl=N,T.scrollableSegments=D,T.hashFragment=O,T.shouldScroll=P,(0,s.handleMutable)(t,T)},()=>t)}}});let r=n(9008),o=n(7391),a=n(8468),i=n(6770),l=n(5951),u=n(2030),c=n(9154),s=n(9435),f=n(6928),d=n(5076),p=n(9752),h=n(3913),g=n(5956),m=n(5334),y=n(7464),v=n(9707);function b(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,s.handleMutable)(e,t)}function R(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,o]of Object.entries(r))for(let r of R(o))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DYNAMIC_STALETIME_MS:function(){return d},STATIC_STALETIME_MS:function(){return p},createSeededPrefetchCacheEntry:function(){return c},getOrCreatePrefetchCacheEntry:function(){return u},prunePrefetchCache:function(){return f}});let r=n(9008),o=n(9154),a=n(5076);function i(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function l(e,t,n){return i(e,t===o.PrefetchKind.FULL,n)}function u(e){let{url:t,nextUrl:n,tree:r,prefetchCache:a,kind:l,allowAliasing:u=!0}=e,c=function(e,t,n,r,a){for(let l of(void 0===t&&(t=o.PrefetchKind.TEMPORARY),[n,null])){let n=i(e,!0,l),u=i(e,!1,l),c=e.search?n:u,s=r.get(c);if(s&&a){if(s.url.pathname===e.pathname&&s.url.search!==e.search)return{...s,aliased:!0};return s}let f=r.get(u);if(a&&e.search&&t!==o.PrefetchKind.FULL&&f&&!f.key.includes("%"))return{...f,aliased:!0}}if(t!==o.PrefetchKind.FULL&&a){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,l,n,a,u);return c?(c.status=h(c),c.kind!==o.PrefetchKind.FULL&&l===o.PrefetchKind.FULL&&c.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return s({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:null!=l?l:o.PrefetchKind.TEMPORARY})}),l&&c.kind===o.PrefetchKind.TEMPORARY&&(c.kind=l),c):s({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:l||o.PrefetchKind.TEMPORARY})}function c(e){let{nextUrl:t,tree:n,prefetchCache:r,url:a,data:i,kind:u}=e,c=i.couldBeIntercepted?l(a,u,t):l(a,u),s={treeAtTimeOfPrefetch:n,data:Promise.resolve(i),kind:u,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:i.staleTime,key:c,status:o.PrefetchCacheEntryStatus.fresh,url:a};return r.set(c,s),s}function s(e){let{url:t,kind:n,tree:i,nextUrl:u,prefetchCache:c}=e,s=l(t,n),f=a.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:i,nextUrl:u,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:o}=e,a=r.get(o);if(!a)return;let i=l(t,a.kind,n);return r.set(i,{...a,key:i}),r.delete(o),i}({url:t,existingCacheKey:s,nextUrl:u,prefetchCache:c})),e.prerendered){let t=c.get(null!=n?n:s);t&&(t.kind=o.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),d={treeAtTimeOfPrefetch:i,data:f,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:s,status:o.PrefetchCacheEntryStatus.fresh,url:t};return c.set(s,d),d}function f(e){for(let[t,n]of e)h(n)===o.PrefetchCacheEntryStatus.expired&&e.delete(t)}let d=1e3*Number("0"),p=1e3*Number("300");function h(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:a}=e;return -1!==a?Date.now()<n+a?o.PrefetchCacheEntryStatus.fresh:o.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+d?r?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.fresh:t===o.PrefetchKind.AUTO&&Date.now()<n+p?o.PrefetchCacheEntryStatus.stale:t===o.PrefetchKind.FULL&&Date.now()<n+p?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var n=function(e){for(var t=[],n=0;n<e.length;){var r=e[n];if("*"===r||"+"===r||"?"===r){t.push({type:"MODIFIER",index:n,value:e[n++]});continue}if("\\"===r){t.push({type:"ESCAPED_CHAR",index:n++,value:e[n++]});continue}if("{"===r){t.push({type:"OPEN",index:n,value:e[n++]});continue}if("}"===r){t.push({type:"CLOSE",index:n,value:e[n++]});continue}if(":"===r){for(var o="",a=n+1;a<e.length;){var i=e.charCodeAt(a);if(i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122||95===i){o+=e[a++];continue}break}if(!o)throw TypeError("Missing parameter name at "+n);t.push({type:"NAME",index:n,value:o}),n=a;continue}if("("===r){var l=1,u="",a=n+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){u+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--l){a++;break}}else if("("===e[a]&&(l++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);u+=e[a++]}if(l)throw TypeError("Unbalanced pattern at "+n);if(!u)throw TypeError("Missing pattern at "+n);t.push({type:"PATTERN",index:n,value:u}),n=a;continue}t.push({type:"CHAR",index:n,value:e[n++]})}return t.push({type:"END",index:n,value:""}),t}(e),r=t.prefixes,a=void 0===r?"./":r,i="[^"+o(t.delimiter||"/#?")+"]+?",l=[],u=0,c=0,s="",f=function(e){if(c<n.length&&n[c].type===e)return n[c++].value},d=function(e){var t=f(e);if(void 0!==t)return t;var r=n[c];throw TypeError("Unexpected "+r.type+" at "+r.index+", expected "+e)},p=function(){for(var e,t="";e=f("CHAR")||f("ESCAPED_CHAR");)t+=e;return t};c<n.length;){var h=f("CHAR"),g=f("NAME"),m=f("PATTERN");if(g||m){var y=h||"";-1===a.indexOf(y)&&(s+=y,y=""),s&&(l.push(s),s=""),l.push({name:g||u++,prefix:y,suffix:"",pattern:m||i,modifier:f("MODIFIER")||""});continue}var v=h||f("ESCAPED_CHAR");if(v){s+=v;continue}if(s&&(l.push(s),s=""),f("OPEN")){var y=p(),b=f("NAME")||"",R=f("PATTERN")||"",_=p();d("CLOSE"),l.push({name:b||(R?u++:""),pattern:b&&!R?i:R,prefix:y,suffix:_,modifier:f("MODIFIER")||""});continue}d("END")}return l}function n(e,t){void 0===t&&(t={});var n=a(t),r=t.encode,o=void 0===r?function(e){return e}:r,i=t.validate,l=void 0===i||i,u=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",n)});return function(t){for(var n="",r=0;r<e.length;r++){var a=e[r];if("string"==typeof a){n+=a;continue}var i=t?t[a.name]:void 0,c="?"===a.modifier||"*"===a.modifier,s="*"===a.modifier||"+"===a.modifier;if(Array.isArray(i)){if(!s)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===i.length){if(c)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var f=0;f<i.length;f++){var d=o(i[f],a);if(l&&!u[r].test(d))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+d+'"');n+=a.prefix+d+a.suffix}continue}if("string"==typeof i||"number"==typeof i){var d=o(String(i),a);if(l&&!u[r].test(d))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+d+'"');n+=a.prefix+d+a.suffix;continue}if(!c){var p=s?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+p)}}return n}}function r(e,t,n){void 0===n&&(n={});var r=n.decode,o=void 0===r?function(e){return e}:r;return function(n){var r=e.exec(n);if(!r)return!1;for(var a=r[0],i=r.index,l=Object.create(null),u=1;u<r.length;u++)!function(e){if(void 0!==r[e]){var n=t[e-1];"*"===n.modifier||"+"===n.modifier?l[n.name]=r[e].split(n.prefix+n.suffix).map(function(e){return o(e,n)}):l[n.name]=o(r[e],n)}}(u);return{path:a,index:i,params:l}}}function o(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function i(e,t,n){void 0===n&&(n={});for(var r=n.strict,i=void 0!==r&&r,l=n.start,u=n.end,c=n.encode,s=void 0===c?function(e){return e}:c,f="["+o(n.endsWith||"")+"]|$",d="["+o(n.delimiter||"/#?")+"]",p=void 0===l||l?"^":"",h=0;h<e.length;h++){var g=e[h];if("string"==typeof g)p+=o(s(g));else{var m=o(s(g.prefix)),y=o(s(g.suffix));if(g.pattern)if(t&&t.push(g),m||y)if("+"===g.modifier||"*"===g.modifier){var v="*"===g.modifier?"?":"";p+="(?:"+m+"((?:"+g.pattern+")(?:"+y+m+"(?:"+g.pattern+"))*)"+y+")"+v}else p+="(?:"+m+"("+g.pattern+")"+y+")"+g.modifier;else p+="("+g.pattern+")"+g.modifier;else p+="(?:"+m+y+")"+g.modifier}}if(void 0===u||u)i||(p+=d+"?"),p+=n.endsWith?"(?="+f+")":"$";else{var b=e[e.length-1],R="string"==typeof b?d.indexOf(b[b.length-1])>-1:void 0===b;i||(p+="(?:"+d+"(?="+f+"))?"),R||(p+="(?="+d+"|"+f+")")}return new RegExp(p,a(n))}function l(t,n,r){if(t instanceof RegExp){if(!n)return t;var o=t.source.match(/\((?!\?)/g);if(o)for(var u=0;u<o.length;u++)n.push({name:u,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return l(e,n,r).source}).join("|")+")",a(r)):i(e(t,r),n,r)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,r){return n(e(t,r),r)},t.tokensToFunction=n,t.match=function(e,t){var n=[];return r(l(e,n,t),n,t)},t.regexpToFunction=r,t.tokensToRegexp=i,t.pathToRegexp=l})(),e.exports=t})()},5416:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return u},isBot:function(){return l}});let r=n(5796),o=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=r.HTML_LIMITED_BOT_UA_RE.source;function i(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function l(e){return o.test(e)||i(e)}function u(e){return o.test(e)?"dom":i(e)?"html":void 0}},5526:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{compileNonPath:function(){return s},matchHas:function(){return c},parseDestination:function(){return f},prepareDestination:function(){return d}});let r=n(5362),o=n(3293),a=n(6759),i=n(1437),l=n(8212);function u(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,n,r){void 0===n&&(n=[]),void 0===r&&(r=[]);let o={},a=n=>{let r,a=n.key;switch(n.type){case"header":a=a.toLowerCase(),r=e.headers[a];break;case"cookie":r="cookies"in e?e.cookies[n.key]:(0,l.getCookieParser)(e.headers)()[n.key];break;case"query":r=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};r=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!n.value&&r)return o[function(e){let t="";for(let n=0;n<e.length;n++){let r=e.charCodeAt(n);(r>64&&r<91||r>96&&r<123)&&(t+=e[n])}return t}(a)]=r,!0;if(r){let e=RegExp("^"+n.value+"$"),t=Array.isArray(r)?r.slice(-1)[0].match(e):r.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{o[e]=t.groups[e]}):"host"===n.type&&t[0]&&(o.host=t[0])),!0}return!1};return!(!n.every(e=>a(e))||r.some(e=>a(e)))&&o}function s(e,t){if(!e.includes(":"))return e;for(let n of Object.keys(t))e.includes(":"+n)&&(e=e.replace(RegExp(":"+n+"\\*","g"),":"+n+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+n+"\\?","g"),":"+n+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+n+"\\+","g"),":"+n+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+n+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+n));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,r.compile)("/"+e,{validate:!1})(t).slice(1)}function f(e){let t=e.destination;for(let n of Object.keys({...e.params,...e.query}))n&&(t=t.replace(RegExp(":"+(0,o.escapeStringRegexp)(n),"g"),"__ESC_COLON_"+n));let n=(0,a.parseUrl)(t),r=n.pathname;r&&(r=u(r));let i=n.href;i&&(i=u(i));let l=n.hostname;l&&(l=u(l));let c=n.hash;return c&&(c=u(c)),{...n,pathname:r,hostname:l,href:i,hash:c}}function d(e){let t,n,o=Object.assign({},e.query),a=f(e),{hostname:l,query:c}=a,d=a.pathname;a.hash&&(d=""+d+a.hash);let p=[],h=[];for(let e of((0,r.pathToRegexp)(d,h),h))p.push(e.name);if(l){let e=[];for(let t of((0,r.pathToRegexp)(l,e),e))p.push(t.name)}let g=(0,r.compile)(d,{validate:!1});for(let[n,o]of(l&&(t=(0,r.compile)(l,{validate:!1})),Object.entries(c)))Array.isArray(o)?c[n]=o.map(t=>s(u(t),e.params)):"string"==typeof o&&(c[n]=s(u(o),e.params));let m=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!m.some(e=>p.includes(e)))for(let t of m)t in c||(c[t]=e.params[t]);if((0,i.isInterceptionRouteAppPath)(d))for(let t of d.split("/")){let n=i.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(n){"(..)(..)"===n?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=n;break}}try{let[r,o]=(n=g(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=r,a.hash=(o?"#":"")+(o||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...o,...a.query},{newUrl:n,destQuery:c,parsedDestination:a}}},5531:(e,t)=>{"use strict";function n(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return n}})},5796:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},5814:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return m},useLinkStatus:function(){return v}});let r=n(740),o=n(687),a=r._(n(3210)),i=n(195),l=n(2142),u=n(9154),c=n(3038),s=n(9289),f=n(6127);n(148);let d=n(3406),p=n(1794),h=n(3690);function g(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function m(e){let t,n,r,[i,m]=(0,a.useOptimistic)(d.IDLE_LINK_STATUS),v=(0,a.useRef)(null),{href:b,as:R,children:_,prefetch:E=null,passHref:w,replace:P,shallow:x,scroll:T,onClick:O,onMouseEnter:j,onTouchStart:S,legacyBehavior:M=!1,onNavigate:A,ref:C,unstable_dynamicOnHover:N,...L}=e;t=_,M&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let k=a.default.useContext(l.AppRouterContext),D=!1!==E,I=null===E?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:U,as:F}=a.default.useMemo(()=>{let e=g(b);return{href:e,as:R?g(R):e}},[b,R]);M&&(n=a.default.Children.only(t));let H=M?n&&"object"==typeof n&&n.ref:C,K=a.default.useCallback(e=>(null!==k&&(v.current=(0,d.mountLinkInstance)(e,U,k,I,D,m)),()=>{v.current&&((0,d.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,d.unmountPrefetchableInstance)(e)}),[D,U,k,I,m]),z={ref:(0,c.useMergedRef)(K,H),onClick(e){M||"function"!=typeof O||O(e),M&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),k&&(e.defaultPrevented||function(e,t,n,r,o,i,l){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(n||t,o?"replace":"push",null==i||i,r.current)})}}(e,U,F,v,P,T,A))},onMouseEnter(e){M||"function"!=typeof j||j(e),M&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),k&&D&&(0,d.onNavigationIntent)(e.currentTarget,!0===N)},onTouchStart:function(e){M||"function"!=typeof S||S(e),M&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),k&&D&&(0,d.onNavigationIntent)(e.currentTarget,!0===N)}};return(0,s.isAbsoluteUrl)(F)?z.href=F:M&&!w&&("a"!==n.type||"href"in n.props)||(z.href=(0,f.addBasePath)(F)),r=M?a.default.cloneElement(n,z):(0,o.jsx)("a",{...L,...z,children:t}),(0,o.jsx)(y.Provider,{value:i,children:r})}n(2708);let y=(0,a.createContext)(d.IDLE_LINK_STATUS),v=()=>(0,a.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5942:(e,t,n)=>{"use strict";function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(6736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5951:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[a,i]=n,[l,u]=t;return(0,o.matchSegment)(l,a)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),i[u]):!!Array.isArray(l)}}});let r=n(4007),o=n(4077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5956:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return h},listenForDynamicRequest:function(){return p},startPPRNavigation:function(){return c},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],o=t.parallelRoutes,i=new Map(o);for(let t in r){let n=r[t],l=n[0],u=(0,a.createRouterCacheKey)(l),c=o.get(t);if(void 0!==c){let r=c.get(u);if(void 0!==r){let o=e(r,n),a=new Map(c);a.set(u,o),i.set(t,a)}}}let l=t.rsc,u=y(l)&&"pending"===l.status;return{lazyData:null,rsc:l,head:t.head,prefetchHead:u?t.prefetchHead:[null,null],prefetchRsc:u?t.prefetchRsc:null,loading:t.loading,parallelRoutes:i,navigatedAt:t.navigatedAt}}}});let r=n(3913),o=n(4077),a=n(3123),i=n(2030),l=n(5334),u={route:null,node:null,dynamicRequestTree:null,children:null};function c(e,t,n,i,l,c,d,p,h){return function e(t,n,i,l,c,d,p,h,g,m,y){let v=i[1],b=l[1],R=null!==d?d[2]:null;c||!0===l[4]&&(c=!0);let _=n.parallelRoutes,E=new Map(_),w={},P=null,x=!1,T={};for(let n in b){let i,l=b[n],f=v[n],d=_.get(n),O=null!==R?R[n]:null,j=l[0],S=m.concat([n,j]),M=(0,a.createRouterCacheKey)(j),A=void 0!==f?f[0]:void 0,C=void 0!==d?d.get(M):void 0;if(null!==(i=j===r.DEFAULT_SEGMENT_KEY?void 0!==f?{route:f,node:null,dynamicRequestTree:null,children:null}:s(t,f,l,C,c,void 0!==O?O:null,p,h,S,y):g&&0===Object.keys(l[1]).length?s(t,f,l,C,c,void 0!==O?O:null,p,h,S,y):void 0!==f&&void 0!==A&&(0,o.matchSegment)(j,A)&&void 0!==C&&void 0!==f?e(t,C,f,l,c,O,p,h,g,S,y):s(t,f,l,C,c,void 0!==O?O:null,p,h,S,y))){if(null===i.route)return u;null===P&&(P=new Map),P.set(n,i);let e=i.node;if(null!==e){let t=new Map(d);t.set(M,e),E.set(n,t)}let t=i.route;w[n]=t;let r=i.dynamicRequestTree;null!==r?(x=!0,T[n]=r):T[n]=t}else w[n]=l,T[n]=l}if(null===P)return null;let O={lazyData:null,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,loading:n.loading,parallelRoutes:E,navigatedAt:t};return{route:f(l,w),node:O,dynamicRequestTree:x?f(l,T):null,children:P}}(e,t,n,i,!1,l,c,d,p,[],h)}function s(e,t,n,r,o,c,s,p,h,g){return!o&&(void 0===t||(0,i.isNavigatingToNewRootLayout)(t,n))?u:function e(t,n,r,o,i,u,c,s){let p,h,g,m,y=n[1],v=0===Object.keys(y).length;if(void 0!==r&&r.navigatedAt+l.DYNAMIC_STALETIME_MS>t)p=r.rsc,h=r.loading,g=r.head,m=r.navigatedAt;else if(null===o)return d(t,n,null,i,u,c,s);else if(p=o[1],h=o[3],g=v?i:null,m=t,o[4]||u&&v)return d(t,n,o,i,u,c,s);let b=null!==o?o[2]:null,R=new Map,_=void 0!==r?r.parallelRoutes:null,E=new Map(_),w={},P=!1;if(v)s.push(c);else for(let n in y){let r=y[n],o=null!==b?b[n]:null,l=null!==_?_.get(n):void 0,f=r[0],d=c.concat([n,f]),p=(0,a.createRouterCacheKey)(f),h=e(t,r,void 0!==l?l.get(p):void 0,o,i,u,d,s);R.set(n,h);let g=h.dynamicRequestTree;null!==g?(P=!0,w[n]=g):w[n]=r;let m=h.node;if(null!==m){let e=new Map;e.set(p,m),E.set(n,e)}}return{route:n,node:{lazyData:null,rsc:p,prefetchRsc:null,head:g,prefetchHead:null,loading:h,parallelRoutes:E,navigatedAt:m},dynamicRequestTree:P?f(n,w):null,children:R}}(e,n,r,c,s,p,h,g)}function f(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function d(e,t,n,r,o,i,l){let u=f(t,t[1]);return u[3]="refetch",{route:t,node:function e(t,n,r,o,i,l,u){let c=n[1],s=null!==r?r[2]:null,f=new Map;for(let n in c){let r=c[n],d=null!==s?s[n]:null,p=r[0],h=l.concat([n,p]),g=(0,a.createRouterCacheKey)(p),m=e(t,r,void 0===d?null:d,o,i,h,u),y=new Map;y.set(g,m),f.set(n,y)}let d=0===f.size;d&&u.push(l);let p=null!==r?r[1]:null,h=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:f,prefetchRsc:void 0!==p?p:null,prefetchHead:d?o:[null,null],loading:void 0!==h?h:null,rsc:v(),head:d?v():null,navigatedAt:t}}(e,t,n,r,o,i,l),dynamicRequestTree:u,children:null}}function p(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:i,head:l}=t;i&&function(e,t,n,r,i){let l=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],a=l.children;if(null!==a){let e=a.get(n);if(void 0!==e){let t=e.route[0];if((0,o.matchSegment)(r,t)){l=e;continue}}}return}!function e(t,n,r,i){if(null===t.dynamicRequestTree)return;let l=t.children,u=t.node;if(null===l){null!==u&&(function e(t,n,r,i,l){let u=n[1],c=r[1],s=i[2],f=t.parallelRoutes;for(let t in u){let n=u[t],r=c[t],i=s[t],d=f.get(t),p=n[0],h=(0,a.createRouterCacheKey)(p),m=void 0!==d?d.get(h):void 0;void 0!==m&&(void 0!==r&&(0,o.matchSegment)(p,r[0])&&null!=i?e(m,n,r,i,l):g(n,m,null))}let d=t.rsc,p=i[1];null===d?t.rsc=p:y(d)&&d.resolve(p);let h=t.head;y(h)&&h.resolve(l)}(u,t.route,n,r,i),t.dynamicRequestTree=null);return}let c=n[1],s=r[2];for(let t in n){let n=c[t],r=s[t],a=l.get(t);if(void 0!==a){let t=a.route[0];if((0,o.matchSegment)(n[0],t)&&null!=r)return e(a,n,r,i)}}}(l,n,r,i)}(e,n,r,i,l)}h(e,null)}},t=>{h(e,t)})}function h(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)g(e.route,n,t);else for(let e of r.values())h(e,t);e.dynamicRequestTree=null}function g(e,t,n){let r=e[1],o=t.parallelRoutes;for(let e in r){let t=r[e],i=o.get(e);if(void 0===i)continue;let l=t[0],u=(0,a.createRouterCacheKey)(l),c=i.get(u);void 0!==c&&g(t,c,n)}let i=t.rsc;y(i)&&(null===n?i.resolve(null):i.reject(n));let l=t.head;y(l)&&l.resolve(null)}let m=Symbol();function y(e){return e&&e.tag===m}function v(){let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=m,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6127:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let r=n(8834),o=n(4674);function a(e,t){return(0,o.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6312:(e,t,n)=>{"use strict";function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},6341:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{getPreviouslyRevalidatedTags:function(){return y},getUtils:function(){return m},interpolateDynamicPath:function(){return h},normalizeDynamicRouteParams:function(){return g},normalizeVercelUrl:function(){return p}});let r=n(9551),o=n(1959),a=n(2437),i=n(4396),l=n(8034),u=n(5526),c=n(2887),s=n(4722),f=n(6143),d=n(7912);function p(e,t,n){let o=(0,r.parse)(e.url,!0);for(let e of(delete o.search,Object.keys(o.query))){let r=e!==f.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(f.NEXT_QUERY_PARAM_PREFIX),a=e!==f.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(f.NEXT_INTERCEPTION_MARKER_PREFIX);(r||a||t.includes(e)||n&&Object.keys(n.groups).includes(e))&&delete o.query[e]}e.url=(0,r.format)(o)}function h(e,t,n){if(!n)return e;for(let r of Object.keys(n.groups)){let o,{optional:a,repeat:i}=n.groups[r],l=`[${i?"...":""}${r}]`;a&&(l=`[${l}]`);let u=t[r];o=Array.isArray(u)?u.map(e=>e&&encodeURIComponent(e)).join("/"):u?encodeURIComponent(u):"",e=e.replaceAll(l,o)}return e}function g(e,t,n,r){let o={};for(let a of Object.keys(t.groups)){let i=e[a];"string"==typeof i?i=(0,s.normalizeRscURL)(i):Array.isArray(i)&&(i=i.map(s.normalizeRscURL));let l=n[a],u=t.groups[a].optional;if((Array.isArray(l)?l.some(e=>Array.isArray(i)?i.some(t=>t.includes(e)):null==i?void 0:i.includes(e)):null==i?void 0:i.includes(l))||void 0===i&&!(u&&r))return{params:{},hasValidParams:!1};u&&(!i||Array.isArray(i)&&1===i.length&&("index"===i[0]||i[0]===`[[...${a}]]`))&&(i=void 0,delete e[a]),i&&"string"==typeof i&&t.groups[a].repeat&&(i=i.split("/")),i&&(o[a]=i)}return{params:o,hasValidParams:!0}}function m({page:e,i18n:t,basePath:n,rewrites:r,pageIsDynamic:s,trailingSlash:f,caseSensitive:m}){let y,v,b;return s&&(y=(0,i.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(v=(0,l.getRouteMatcher)(y))(e)),{handleRewrites:function(i,l){let d={},p=l.pathname,h=r=>{let c=(0,a.getPathMatch)(r.source+(f?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!m});if(!l.pathname)return!1;let h=c(l.pathname);if((r.has||r.missing)&&h){let e=(0,u.matchHas)(i,l.query,r.has,r.missing);e?Object.assign(h,e):h=!1}if(h){let{parsedDestination:a,destQuery:i}=(0,u.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:h,query:l.query});if(a.protocol)return!0;if(Object.assign(d,i,h),Object.assign(l.query,a.query),delete a.query,Object.assign(l,a),!(p=l.pathname))return!1;if(n&&(p=p.replace(RegExp(`^${n}`),"")||"/"),t){let e=(0,o.normalizeLocalePath)(p,t.locales);p=e.pathname,l.query.nextInternalLocale=e.detectedLocale||h.nextInternalLocale}if(p===e)return!0;if(s&&v){let e=v(p);if(e)return l.query={...l.query,...e},!0}}return!1};for(let e of r.beforeFiles||[])h(e);if(p!==e){let t=!1;for(let e of r.afterFiles||[])if(t=h(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(p||"");return t===(0,c.removeTrailingSlash)(e)||(null==v?void 0:v(t))})()){for(let e of r.fallback||[])if(t=h(e))break}}return d},defaultRouteRegex:y,dynamicRouteMatcher:v,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!y)return null;let{groups:t,routeKeys:n}=y,r=(0,l.getRouteMatcher)({re:{exec:e=>{let r=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(r)){let n=(0,d.normalizeNextQueryParam)(e);n&&(r[n]=t,delete r[e])}let o={};for(let e of Object.keys(n)){let a=n[e];if(!a)continue;let i=t[a],l=r[e];if(!i.optional&&!l)return null;o[i.pos]=l}return o}},groups:t})(e);return r||null},normalizeDynamicRouteParams:(e,t)=>y&&b?g(e,y,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,y),interpolateDynamicPath:(e,t)=>h(e,t,y)}}function y(e,t){return"string"==typeof e[f.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[f.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[f.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6361:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return o}});let r=n(6127);function o(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,n){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var o={},a=t.split(r),i=(n||{}).decode||e,l=0;l<a.length;l++){var u=a[l],c=u.indexOf("=");if(!(c<0)){var s=u.substr(0,c).trim(),f=u.substr(++c,u.length).trim();'"'==f[0]&&(f=f.slice(1,-1)),void 0==o[s]&&(o[s]=function(e,t){try{return t(e)}catch(t){return e}}(f,i))}}return o},t.serialize=function(e,t,r){var a=r||{},i=a.encode||n;if("function"!=typeof i)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var l=i(t);if(l&&!o.test(l))throw TypeError("argument val is invalid");var u=e+"="+l;if(null!=a.maxAge){var c=a.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");u+="; Max-Age="+Math.floor(c)}if(a.domain){if(!o.test(a.domain))throw TypeError("option domain is invalid");u+="; Domain="+a.domain}if(a.path){if(!o.test(a.path))throw TypeError("option path is invalid");u+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");u+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(u+="; HttpOnly"),a.secure&&(u+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":u+="; SameSite=Strict";break;case"lax":u+="; SameSite=Lax";break;case"none":u+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return u};var e=decodeURIComponent,n=encodeURIComponent,r=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6493:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return o}});let r=n(5232);function o(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6715:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},6736:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return o}});let r=n(2255);function o(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6759:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let r=n(2785),o=n(3736);function a(e){if(e.startsWith("/"))return(0,o.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,r.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6770:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,u){let c,[s,f,d,p,h]=n;if(1===t.length){let e=l(n,r);return(0,i.addRefreshMarkerToActiveParallelSegments)(e,u),e}let[g,m]=t;if(!(0,a.matchSegment)(g,s))return null;if(2===t.length)c=l(f[m],r);else if(null===(c=e((0,o.getNextFlightSegmentPath)(t),f[m],r,u)))return null;let y=[t[0],{...f,[m]:c},d,p];return h&&(y[4]=!0),(0,i.addRefreshMarkerToActiveParallelSegments)(y,u),y}}});let r=n(3913),o=n(4007),a=n(4077),i=n(2308);function l(e,t){let[n,o]=e,[i,u]=t;if(i===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(n,i)){let t={};for(let e in o)void 0!==u[e]?t[e]=l(o[e],u[e]):t[e]=o[e];for(let e in u)t[e]||(t[e]=u[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6928:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let r=n(1500),o=n(3898);function a(e,t,n,a,i){let{tree:l,seedData:u,head:c,isRootRender:s}=a;if(null===u)return!1;if(s){let o=u[1];n.loading=u[3],n.rsc=o,n.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(e,n,t,l,u,c,i)}else n.rsc=t.rsc,n.prefetchRsc=t.prefetchRsc,n.parallelRoutes=new Map(t.parallelRoutes),n.loading=t.loading,(0,o.fillCacheWithNewSubTreeData)(e,n,t,a,i);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6963:(e,t,n)=>{"use strict";n.d(t,{B:()=>u});var r,o=n(3210),a=n(6156),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(i());return(0,a.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},7022:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return i}});let r=n(3210),o=n(1215),a="next-route-announcer";function i(e){let{tree:t}=e,[n,i]=(0,r.useState)(null);(0,r.useEffect)(()=>(i(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[l,u]=(0,r.useState)(""),c=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==c.current&&c.current!==e&&u(e),c.current=e},[t]),n?(0,o.createPortal)(l,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7464:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let i=a.length<=2,[l,u]=a,c=(0,o.createRouterCacheKey)(u),s=n.parallelRoutes.get(l),f=t.parallelRoutes.get(l);f&&f!==s||(f=new Map(s),t.parallelRoutes.set(l,f));let d=null==s?void 0:s.get(c),p=f.get(c);if(i){p&&p.lazyData&&p!==d||f.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!d){p||f.set(c,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===d&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},f.set(c,p)),e(p,d,(0,r.getNextFlightSegmentPath)(a))}}});let r=n(4007),o=n(3123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7810:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return S}});let r=n(1264),o=n(1448),a=n(1563),i=n(9154),l=n(6361),u=n(7391),c=n(5232),s=n(6770),f=n(2030),d=n(9435),p=n(1500),h=n(9752),g=n(8214),m=n(6493),y=n(2308),v=n(4007),b=n(6875),R=n(7860),_=n(5334),E=n(5942),w=n(6736),P=n(4642);n(593);let{createFromFetch:x,createTemporaryReferenceSet:T,encodeReply:O}=n(9357);async function j(e,t,n){let i,u,{actionId:c,actionArgs:s}=n,f=T(),d=(0,P.extractInfoFromServerReferenceId)(c),p="use-cache"===d.type?(0,P.omitUnusedArgs)(s,d):s,h=await O(p,{temporaryReferences:f}),g=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:c,[a.NEXT_ROUTER_STATE_TREE_HEADER]:(0,v.prepareFlightRouterStateForRequest)(e.tree),...{},...t?{[a.NEXT_URL]:t}:{}},body:h}),m=g.headers.get("x-action-redirect"),[y,b]=(null==m?void 0:m.split(";"))||[];switch(b){case"push":i=R.RedirectType.push;break;case"replace":i=R.RedirectType.replace;break;default:i=void 0}let _=!!g.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(g.headers.get("x-action-revalidated")||"[[],0,0]");u={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){u={paths:[],tag:!1,cookie:!1}}let E=y?(0,l.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,w=g.headers.get("content-type");if(null==w?void 0:w.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await x(Promise.resolve(g),{callServer:r.callServer,findSourceMapURL:o.findSourceMapURL,temporaryReferences:f});return y?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:E,redirectType:i,revalidatedParts:u,isPrerender:_}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:E,redirectType:i,revalidatedParts:u,isPrerender:_}}if(g.status>=400)throw Object.defineProperty(Error("text/plain"===w?await g.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:E,redirectType:i,revalidatedParts:u,isPrerender:_}}function S(e,t){let{resolve:n,reject:r}=t,o={},a=e.tree;o.preserveCustomHistoryState=!1;let l=e.nextUrl&&(0,g.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return j(e,l,t).then(async g=>{let P,{actionResult:x,actionFlightData:T,redirectLocation:O,redirectType:j,isPrerender:S,revalidatedParts:M}=g;if(O&&(j===R.RedirectType.replace?(e.pushRef.pendingPush=!1,o.pendingPush=!1):(e.pushRef.pendingPush=!0,o.pendingPush=!0),o.canonicalUrl=P=(0,u.createHrefFromUrl)(O,!1)),!T)return(n(x),O)?(0,c.handleExternalUrl)(e,o,O.href,e.pushRef.pendingPush):e;if("string"==typeof T)return n(x),(0,c.handleExternalUrl)(e,o,T,e.pushRef.pendingPush);let A=M.paths.length>0||M.tag||M.cookie;for(let r of T){let{tree:i,seedData:u,head:d,isRootRender:g}=r;if(!g)return console.log("SERVER ACTION APPLY FAILED"),n(x),e;let b=(0,s.applyRouterStatePatchToTree)([""],a,i,P||e.canonicalUrl);if(null===b)return n(x),(0,m.handleSegmentMismatch)(e,t,i);if((0,f.isNavigatingToNewRootLayout)(a,b))return n(x),(0,c.handleExternalUrl)(e,o,P||e.canonicalUrl,e.pushRef.pendingPush);if(null!==u){let t=u[1],n=(0,h.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=u[3],(0,p.fillLazyItemsTillLeafWithHead)(v,n,void 0,i,u,d,void 0),o.cache=n,o.prefetchCache=new Map,A&&await (0,y.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:b,updatedCache:n,includeNextUrl:!!l,canonicalUrl:o.canonicalUrl||e.canonicalUrl})}o.patchedTree=b,a=b}return O&&P?(A||((0,_.createSeededPrefetchCacheEntry)({url:O,data:{flightData:T,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:S?i.PrefetchKind.FULL:i.PrefetchKind.AUTO}),o.prefetchCache=e.prefetchCache),r((0,b.getRedirectError)((0,w.hasBasePath)(P)?(0,E.removeBasePath)(P):P,j||R.RedirectType.push))):n(x),(0,d.handleMutable)(e,o)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7936:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(9008),n(7391),n(6770),n(2030),n(5232),n(9435),n(6928),n(9752),n(6493),n(8214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8034:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let r=n(4827);function o(e){let{re:t,groups:n}=e;return e=>{let o=t.exec(e);if(!o)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new r.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},i={};for(let[e,t]of Object.entries(n)){let n=o[t.pos];void 0!==n&&(t.repeat?i[e]=n.split("/").map(e=>a(e)):i[e]=a(n))}return i}}},8212:(e,t,n)=>{"use strict";function r(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:r}=n(6415);return r(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return r}})},8304:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return l},STATIC_METADATA_IMAGES:function(){return i},getExtensionRegexString:function(){return u},isMetadataPage:function(){return f},isMetadataRoute:function(){return d},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return s}});let r=n(2958),o=n(4722),a=n(554),i={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},l=["js","jsx","ts","tsx"],u=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,n){let o=(n?"":"?")+"$",a=`\\d?${n?"":"(-\\w{6})?"}`,l=[RegExp(`^[\\\\/]robots${u(t.concat("txt"),null)}${o}`),RegExp(`^[\\\\/]manifest${u(t.concat("webmanifest","json"),null)}${o}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${u(["xml"],t)}${o}`),RegExp(`[\\\\/]${i.icon.filename}${a}${u(i.icon.extensions,t)}${o}`),RegExp(`[\\\\/]${i.apple.filename}${a}${u(i.apple.extensions,t)}${o}`),RegExp(`[\\\\/]${i.openGraph.filename}${a}${u(i.openGraph.extensions,t)}${o}`),RegExp(`[\\\\/]${i.twitter.filename}${a}${u(i.twitter.extensions,t)}${o}`)],c=(0,r.normalizePathSep)(e);return l.some(e=>e.test(c))}function s(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function f(e){return!(0,a.isAppRouteRoute)(e)&&c(e,[],!1)}function d(e){let t=(0,o.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&c(t,[],!1)}},8468:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let i=a.length<=2,[l,u]=a,c=(0,r.createRouterCacheKey)(u),s=n.parallelRoutes.get(l);if(!s)return;let f=t.parallelRoutes.get(l);if(f&&f!==s||(f=new Map(s),t.parallelRoutes.set(l,f)),i)return void f.delete(c);let d=s.get(c),p=f.get(c);p&&d&&(p===d&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},f.set(c,p)),e(p,d,(0,o.getNextFlightSegmentPath)(a)))}}});let r=n(3123),o=n(4007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8627:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let r=n(7391),o=n(642);function a(e,t){var n;let{url:a,tree:i}=t,l=(0,r.createHrefFromUrl)(a),u=i||e.tree,c=e.cache;return{canonicalUrl:l,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:c,prefetchCache:e.prefetchCache,tree:u,nextUrl:null!=(n=(0,o.extractPathFromFlightRouterState)(u))?n:a.pathname}}n(5956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8674:(e,t,n)=>{"use strict";n.d(t,{Mz:()=>e8,i3:()=>tt,UC:()=>te,bL:()=>e7,Bk:()=>eG});var r=n(3210);let o=["top","right","bottom","left"],a=Math.min,i=Math.max,l=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function g(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}let y=new Set(["top","bottom"]);function v(e){return y.has(p(e))?"y":"x"}function b(e){return e.replace(/start|end/g,e=>f[e])}let R=["left","right"],_=["right","left"],E=["top","bottom"],w=["bottom","top"];function P(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function T(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function O(e,t,n){let r,{reference:o,floating:a}=e,i=v(t),l=g(v(t)),u=m(l),c=p(t),s="y"===i,f=o.x+o.width/2-a.width/2,d=o.y+o.height/2-a.height/2,y=o[u]/2-a[u]/2;switch(c){case"top":r={x:f,y:o.y-a.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-a.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=y*(n&&s?-1:1);break;case"end":r[l]+=y*(n&&s?-1:1)}return r}let j=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i}=n,l=a.filter(Boolean),u=await (null==i.isRTL?void 0:i.isRTL(t)),c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=O(c,r,u),d=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:a,fn:g}=l[n],{x:m,y:y,data:v,reset:b}=await g({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:i,elements:{reference:e,floating:t}});s=null!=m?m:s,f=null!=y?y:f,p={...p,[a]:{...p[a],...v}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(d=b.placement),b.rects&&(c=!0===b.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:s,y:f}=O(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function S(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:a,rects:i,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),g=x(h),m=l[p?"floating"===f?"reference":"floating":f],y=T(await a.getClippingRect({element:null==(n=await (null==a.isElement?void 0:a.isElement(m)))||n?m:m.contextElement||await (null==a.getDocumentElement?void 0:a.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),v="floating"===f?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(l.floating)),R=await (null==a.isElement?void 0:a.isElement(b))&&await (null==a.getScale?void 0:a.getScale(b))||{x:1,y:1},_=T(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:v,offsetParent:b,strategy:u}):v);return{top:(y.top-_.top+g.top)/R.y,bottom:(_.bottom-y.bottom+g.bottom)/R.y,left:(y.left-_.left+g.left)/R.x,right:(_.right-y.right+g.right)/R.x}}function M(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function A(e){return o.some(t=>e[t]>=0)}let C=new Set(["left","top"]);async function N(e,t){let{placement:n,platform:r,elements:o}=e,a=await (null==r.isRTL?void 0:r.isRTL(o.floating)),i=p(n),l=h(n),u="y"===v(n),c=C.has(i)?-1:1,s=a&&u?-1:1,f=d(t,e),{mainAxis:g,crossAxis:m,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&"number"==typeof y&&(m="end"===l?-1*y:y),u?{x:m*s,y:g*c}:{x:g*c,y:m*s}}function L(){return"undefined"!=typeof window}function k(e){return U(e)?(e.nodeName||"").toLowerCase():"#document"}function D(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function I(e){var t;return null==(t=(U(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function U(e){return!!L()&&(e instanceof Node||e instanceof D(e).Node)}function F(e){return!!L()&&(e instanceof Element||e instanceof D(e).Element)}function H(e){return!!L()&&(e instanceof HTMLElement||e instanceof D(e).HTMLElement)}function K(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof D(e).ShadowRoot)}let z=new Set(["inline","contents"]);function B(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!z.has(o)}let W=new Set(["table","td","th"]),$=[":popover-open",":modal"];function G(e){return $.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let q=["transform","translate","scale","rotate","perspective"],X=["transform","translate","scale","rotate","perspective","filter"],V=["paint","layout","strict","content"];function Y(e){let t=Q(),n=F(e)?ee(e):e;return q.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||X.some(e=>(n.willChange||"").includes(e))||V.some(e=>(n.contain||"").includes(e))}function Q(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Z=new Set(["html","body","#document"]);function J(e){return Z.has(k(e))}function ee(e){return D(e).getComputedStyle(e)}function et(e){return F(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===k(e))return e;let t=e.assignedSlot||e.parentNode||K(e)&&e.host||I(e);return K(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return J(n)?t.ownerDocument?t.ownerDocument.body:t.body:H(n)&&B(n)?n:e(n)}(e),a=o===(null==(r=e.ownerDocument)?void 0:r.body),i=D(o);if(a){let e=eo(i);return t.concat(i,i.visualViewport||[],B(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ea(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=H(e),a=o?e.offsetWidth:n,i=o?e.offsetHeight:r,u=l(n)!==a||l(r)!==i;return u&&(n=a,r=i),{width:n,height:r,$:u}}function ei(e){return F(e)?e:e.contextElement}function el(e){let t=ei(e);if(!H(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:a}=ea(t),i=(a?l(n.width):n.width)/r,u=(a?l(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),u&&Number.isFinite(u)||(u=1),{x:i,y:u}}let eu=c(0);function ec(e){let t=D(e);return Q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function es(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let a=e.getBoundingClientRect(),i=ei(e),l=c(1);t&&(r?F(r)&&(l=el(r)):l=el(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===D(i))&&o)?ec(i):c(0),s=(a.left+u.x)/l.x,f=(a.top+u.y)/l.y,d=a.width/l.x,p=a.height/l.y;if(i){let e=D(i),t=r&&F(r)?D(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=el(o),t=o.getBoundingClientRect(),r=ee(o),a=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=a,f+=i,o=eo(n=D(o))}}return T({width:d,height:p,x:s,y:f})}function ef(e,t){let n=et(e).scrollLeft;return t?t.left+n:es(I(e)).left+n}function ed(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ef(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=D(e),r=I(e),o=n.visualViewport,a=r.clientWidth,i=r.clientHeight,l=0,u=0;if(o){a=o.width,i=o.height;let e=Q();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:a,height:i,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=I(e),n=et(e),r=e.ownerDocument.body,o=i(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=i(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ef(e),u=-n.scrollTop;return"rtl"===ee(r).direction&&(l+=i(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:l,y:u}}(I(e));else if(F(t))r=function(e,t){let n=es(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=H(e)?el(e):c(1),i=e.clientWidth*a.x,l=e.clientHeight*a.y;return{width:i,height:l,x:o*a.x,y:r*a.y}}(t,n);else{let n=ec(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return T(r)}function eg(e){return"static"===ee(e).position}function em(e,t){if(!H(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return I(e)===n&&(n=n.ownerDocument.body),n}function ey(e,t){var n;let r=D(e);if(G(e))return r;if(!H(e)){let t=en(e);for(;t&&!J(t);){if(F(t)&&!eg(t))return t;t=en(t)}return r}let o=em(e,t);for(;o&&(n=o,W.has(k(n)))&&eg(o);)o=em(o,t);return o&&J(o)&&eg(o)&&!Y(o)?r:o||function(e){let t=en(e);for(;H(t)&&!J(t);){if(Y(t))return t;if(G(t))break;t=en(t)}return null}(e)||r}let ev=async function(e){let t=this.getOffsetParent||ey,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=H(t),o=I(t),a="fixed"===n,i=es(e,!0,a,t),l={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!a)if(("body"!==k(t)||B(o))&&(l=et(t)),r){let e=es(t,!0,a,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ef(o));a&&!r&&o&&(u.x=ef(o));let s=!o||r||a?c(0):ed(o,l);return{x:i.left+l.scrollLeft-u.x-s.x,y:i.top+l.scrollTop-u.y-s.y,width:i.width,height:i.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eb={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,a="fixed"===o,i=I(r),l=!!t&&G(t.floating);if(r===i||l&&a)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),f=c(0),d=H(r);if((d||!d&&!a)&&(("body"!==k(r)||B(i))&&(u=et(r)),H(r))){let e=es(r);s=el(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!i||d||a?c(0):ed(i,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-u.scrollTop*s.y+f.y+p.y}},getDocumentElement:I,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?G(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>F(e)&&"body"!==k(e)),o=null,a="fixed"===ee(e).position,i=a?en(e):e;for(;F(i)&&!J(i);){let t=ee(i),n=Y(i);n||"fixed"!==t.position||(o=null),(a?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||B(i)&&!n&&function e(t,n){let r=en(t);return!(r===n||!F(r)||J(r))&&("fixed"===ee(r).position||e(r,n))}(e,i))?r=r.filter(e=>e!==i):o=t,i=en(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],c=l.reduce((e,n)=>{let r=eh(t,n,o);return e.top=i(r.top,e.top),e.right=a(r.right,e.right),e.bottom=a(r.bottom,e.bottom),e.left=i(r.left,e.left),e},eh(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:ey,getElementRects:ev,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ea(e);return{width:t,height:n}},getScale:el,isElement:F,isRTL:function(e){return"rtl"===ee(e).direction}};function eR(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let e_=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:c,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=x(p),b={x:n,y:r},R=g(v(o)),_=m(R),E=await u.getDimensions(f),w="y"===R,P=w?"clientHeight":"clientWidth",T=l.reference[_]+l.reference[R]-b[R]-l.floating[_],O=b[R]-l.reference[R],j=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),S=j?j[P]:0;S&&await (null==u.isElement?void 0:u.isElement(j))||(S=c.floating[P]||l.floating[_]);let M=S/2-E[_]/2-1,A=a(y[w?"top":"left"],M),C=a(y[w?"bottom":"right"],M),N=S-E[_]-C,L=S/2-E[_]/2+(T/2-O/2),k=i(A,a(L,N)),D=!s.arrow&&null!=h(o)&&L!==k&&l.reference[_]/2-(L<A?A:C)-E[_]/2<0,I=D?L<A?L-A:L-N:0;return{[R]:b[R]+I,data:{[R]:k,centerOffset:L-k-I,...D&&{alignmentOffset:I}},reset:D}}}),eE=(e,t,n)=>{let r=new Map,o={platform:eb,...n},a={...o.platform,_c:r};return j(e,t,{...o,platform:a})};var ew=n(1215),eP="undefined"!=typeof document?r.useLayoutEffect:function(){};function ex(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ex(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ex(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eT(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eO(e,t){let n=eT(e);return Math.round(t*n)/n}function ej(e){let t=r.useRef(e);return eP(()=>{t.current=e}),t}let eS=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?e_({element:n.current,padding:r}).fn(t):{}:n?e_({element:n,padding:r}).fn(t):{}}}),eM=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:a,placement:i,middlewareData:l}=t,u=await N(t,e);return i===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:a+u.y,data:{...u,placement:i}}}}}(e),options:[e,t]}),eA=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),f={x:n,y:r},h=await S(t,s),m=v(p(o)),y=g(m),b=f[y],R=f[m];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=b+h[e],r=b-h[t];b=i(n,a(b,r))}if(u){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=R+h[e],r=R-h[t];R=i(n,a(R,r))}let _=c.fn({...t,[y]:b,[m]:R});return{..._,data:{x:_.x-n,y:_.y-r,enabled:{[y]:l,[m]:u}}}}}}(e),options:[e,t]}),eC=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:a,middlewareData:i}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=d(e,t),s={x:n,y:r},f=v(o),h=g(f),m=s[h],y=s[f],b=d(l,t),R="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(u){let e="y"===h?"height":"width",t=a.reference[h]-a.floating[e]+R.mainAxis,n=a.reference[h]+a.reference[e]-R.mainAxis;m<t?m=t:m>n&&(m=n)}if(c){var _,E;let e="y"===h?"width":"height",t=C.has(p(o)),n=a.reference[f]-a.floating[e]+(t&&(null==(_=i.offset)?void 0:_[f])||0)+(t?0:R.crossAxis),r=a.reference[f]+a.reference[e]+(t?0:(null==(E=i.offset)?void 0:E[f])||0)-(t?R.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:m,[f]:y}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,a,i;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:f,elements:y}=t,{mainAxis:x=!0,crossAxis:T=!0,fallbackPlacements:O,fallbackStrategy:j="bestFit",fallbackAxisSideDirection:M="none",flipAlignment:A=!0,...C}=d(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let N=p(l),L=v(s),k=p(s)===s,D=await (null==f.isRTL?void 0:f.isRTL(y.floating)),I=O||(k||!A?[P(s)]:function(e){let t=P(e);return[b(e),t,b(t)]}(s)),U="none"!==M;!O&&U&&I.push(...function(e,t,n,r){let o=h(e),a=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?_:R;return t?R:_;case"left":case"right":return t?E:w;default:return[]}}(p(e),"start"===n,r);return o&&(a=a.map(e=>e+"-"+o),t&&(a=a.concat(a.map(b)))),a}(s,A,M,D));let F=[s,...I],H=await S(t,C),K=[],z=(null==(r=u.flip)?void 0:r.overflows)||[];if(x&&K.push(H[N]),T){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=g(v(e)),a=m(o),i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=P(i)),[i,P(i)]}(l,c,D);K.push(H[e[0]],H[e[1]])}if(z=[...z,{placement:l,overflows:K}],!K.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=F[e];if(t&&("alignment"!==T||L===v(t)||z.every(e=>v(e.placement)!==L||e.overflows[0]>0)))return{data:{index:e,overflows:z},reset:{placement:t}};let n=null==(a=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!n)switch(j){case"bestFit":{let e=null==(i=z.filter(e=>{if(U){let t=v(e.placement);return t===L||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:i[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:u,rects:c,platform:s,elements:f}=t,{apply:g=()=>{},...m}=d(e,t),y=await S(t,m),b=p(u),R=h(u),_="y"===v(u),{width:E,height:w}=c.floating;"top"===b||"bottom"===b?(o=b,l=R===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(l=b,o="end"===R?"top":"bottom");let P=w-y.top-y.bottom,x=E-y.left-y.right,T=a(w-y[o],P),O=a(E-y[l],x),j=!t.middlewareData.shift,M=T,A=O;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(A=x),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(M=P),j&&!R){let e=i(y.left,0),t=i(y.right,0),n=i(y.top,0),r=i(y.bottom,0);_?A=E-2*(0!==e||0!==t?e+t:i(y.left,y.right)):M=w-2*(0!==n||0!==r?n+r:i(y.top,y.bottom))}await g({...t,availableWidth:A,availableHeight:M});let C=await s.getDimensions(f.floating);return E!==C.width||w!==C.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ek=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=M(await S(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:A(e)}}}case"escaped":{let e=M(await S(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:A(e)}}}default:return{}}}}}(e),options:[e,t]}),eD=(e,t)=>({...eS(e),options:[e,t]});var eI=n(4163),eU=n(687),eF=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...a}=e;return(0,eU.jsx)(eI.sG.svg,{...a,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eU.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eF.displayName="Arrow";var eH=n(8599),eK=n(1273),ez=n(3495),eB=n(6156),eW="Popper",[e$,eG]=(0,eK.A)(eW),[eq,eX]=e$(eW),eV=e=>{let{__scopePopper:t,children:n}=e,[o,a]=r.useState(null);return(0,eU.jsx)(eq,{scope:t,anchor:o,onAnchorChange:a,children:n})};eV.displayName=eW;var eY="PopperAnchor",eQ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...a}=e,i=eX(eY,n),l=r.useRef(null),u=(0,eH.s)(t,l);return r.useEffect(()=>{i.onAnchorChange(o?.current||l.current)}),o?null:(0,eU.jsx)(eI.sG.div,{...a,ref:u})});eQ.displayName=eY;var eZ="PopperContent",[eJ,e0]=e$(eZ),e1=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:c="center",alignOffset:s=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:g="partial",hideWhenDetached:m=!1,updatePositionStrategy:y="optimized",onPlaced:v,...b}=e,R=eX(eZ,n),[_,E]=r.useState(null),w=(0,eH.s)(t,e=>E(e)),[P,x]=r.useState(null),T=function(e){let[t,n]=r.useState(void 0);return(0,eB.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(P),O=T?.width??0,j=T?.height??0,S="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},M=Array.isArray(p)?p:[p],A=M.length>0,C={padding:S,boundary:M.filter(e9),altBoundary:A},{refs:N,floatingStyles:L,placement:k,isPositioned:D,middlewareData:U}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:a,elements:{reference:i,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ex(p,o)||h(o);let[g,m]=r.useState(null),[y,v]=r.useState(null),b=r.useCallback(e=>{e!==w.current&&(w.current=e,m(e))},[]),R=r.useCallback(e=>{e!==P.current&&(P.current=e,v(e))},[]),_=i||g,E=l||y,w=r.useRef(null),P=r.useRef(null),x=r.useRef(f),T=null!=c,O=ej(c),j=ej(a),S=ej(s),M=r.useCallback(()=>{if(!w.current||!P.current)return;let e={placement:t,strategy:n,middleware:p};j.current&&(e.platform=j.current),eE(w.current,P.current,e).then(e=>{let t={...e,isPositioned:!1!==S.current};A.current&&!ex(x.current,t)&&(x.current=t,ew.flushSync(()=>{d(t)}))})},[p,t,n,j,S]);eP(()=>{!1===s&&x.current.isPositioned&&(x.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let A=r.useRef(!1);eP(()=>(A.current=!0,()=>{A.current=!1}),[]),eP(()=>{if(_&&(w.current=_),E&&(P.current=E),_&&E){if(O.current)return O.current(_,E,M);M()}},[_,E,M,O,T]);let C=r.useMemo(()=>({reference:w,floating:P,setReference:b,setFloating:R}),[b,R]),N=r.useMemo(()=>({reference:_,floating:E}),[_,E]),L=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=eO(N.floating,f.x),r=eO(N.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eT(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,N.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:M,refs:C,elements:N,floatingStyles:L}),[f,M,C,N,L])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=ei(e),h=l||c?[...p?er(p):[],...er(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let g=p&&f?function(e,t){let n,r=null,o=I(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),l();let d=e.getBoundingClientRect(),{left:p,top:h,width:g,height:m}=d;if(s||t(),!g||!m)return;let y=u(h),v=u(o.clientWidth-(p+g)),b={rootMargin:-y+"px "+-v+"px "+-u(o.clientHeight-(h+m))+"px "+-u(p)+"px",threshold:i(0,a(1,f))||1},R=!0;function _(t){let r=t[0].intersectionRatio;if(r!==f){if(!R)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||eR(d,e.getBoundingClientRect())||c(),R=!1}try{r=new IntersectionObserver(_,{...b,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(_,b)}r.observe(e)}(!0),l}(p,n):null,m=-1,y=null;s&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let v=d?es(e):null;return d&&function t(){let r=es(e);v&&!eR(v,r)&&n(),v=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==g||g(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===y}),elements:{reference:R.anchor},middleware:[eM({mainAxis:l+j,alignmentAxis:s}),d&&eA({mainAxis:!0,crossAxis:!1,limiter:"partial"===g?eC():void 0,...C}),d&&eN({...C}),eL({...C,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:a}=t.reference,i=e.floating.style;i.setProperty("--radix-popper-available-width",`${n}px`),i.setProperty("--radix-popper-available-height",`${r}px`),i.setProperty("--radix-popper-anchor-width",`${o}px`),i.setProperty("--radix-popper-anchor-height",`${a}px`)}}),P&&eD({element:P,padding:f}),e4({arrowWidth:O,arrowHeight:j}),m&&ek({strategy:"referenceHidden",...C})]}),[F,H]=e6(k),K=(0,ez.c)(v);(0,eB.N)(()=>{D&&K?.()},[D,K]);let z=U.arrow?.x,B=U.arrow?.y,W=U.arrow?.centerOffset!==0,[$,G]=r.useState();return(0,eB.N)(()=>{_&&G(window.getComputedStyle(_).zIndex)},[_]),(0,eU.jsx)("div",{ref:N.setFloating,"data-radix-popper-content-wrapper":"",style:{...L,transform:D?L.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:$,"--radix-popper-transform-origin":[U.transformOrigin?.x,U.transformOrigin?.y].join(" "),...U.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eU.jsx)(eJ,{scope:n,placedSide:F,onArrowChange:x,arrowX:z,arrowY:B,shouldHideArrow:W,children:(0,eU.jsx)(eI.sG.div,{"data-side":F,"data-align":H,...b,ref:w,style:{...b.style,animation:D?void 0:"none"}})})})});e1.displayName=eZ;var e2="PopperArrow",e3={top:"bottom",right:"left",bottom:"top",left:"right"},e5=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e0(e2,n),a=e3[o.placedSide];return(0,eU.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eU.jsx)(eF,{...r,ref:t,style:{...r.style,display:"block"}})})});function e9(e){return null!==e}e5.displayName=e2;var e4=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,a=o.arrow?.centerOffset!==0,i=a?0:e.arrowWidth,l=a?0:e.arrowHeight,[u,c]=e6(n),s={start:"0%",center:"50%",end:"100%"}[c],f=(o.arrow?.x??0)+i/2,d=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===u?(p=a?s:`${f}px`,h=`${-l}px`):"top"===u?(p=a?s:`${f}px`,h=`${r.floating.height+l}px`):"right"===u?(p=`${-l}px`,h=a?s:`${d}px`):"left"===u&&(p=`${r.floating.width+l}px`,h=a?s:`${d}px`),{data:{x:p,y:h}}}});function e6(e){let[t,n="center"]=e.split("-");return[t,n]}var e7=eV,e8=eQ,te=e1,tt=e5},8830:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(9154),n(5232),n(9651),n(8627),n(8866),n(5076),n(7936),n(7810);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8834:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let r=n(1550);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:o,hash:a}=(0,r.parsePath)(e);return""+t+n+o+a}},8866:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return h}});let r=n(9008),o=n(7391),a=n(6770),i=n(2030),l=n(5232),u=n(9435),c=n(1500),s=n(9752),f=n(6493),d=n(8214),p=n(2308);function h(e,t){let{origin:n}=t,h={},g=e.canonicalUrl,m=e.tree;h.preserveCustomHistoryState=!1;let y=(0,s.createEmptyCacheNode)(),v=(0,d.hasInterceptionRouteInCurrentTree)(e.tree);y.lazyData=(0,r.fetchServerResponse)(new URL(g,n),{flightRouterState:[m[0],m[1],m[2],"refetch"],nextUrl:v?e.nextUrl:null});let b=Date.now();return y.lazyData.then(async n=>{let{flightData:r,canonicalUrl:s}=n;if("string"==typeof r)return(0,l.handleExternalUrl)(e,h,r,e.pushRef.pendingPush);for(let n of(y.lazyData=null,r)){let{tree:r,seedData:u,head:d,isRootRender:R}=n;if(!R)return console.log("REFRESH FAILED"),e;let _=(0,a.applyRouterStatePatchToTree)([""],m,r,e.canonicalUrl);if(null===_)return(0,f.handleSegmentMismatch)(e,t,r);if((0,i.isNavigatingToNewRootLayout)(m,_))return(0,l.handleExternalUrl)(e,h,g,e.pushRef.pendingPush);let E=s?(0,o.createHrefFromUrl)(s):void 0;if(s&&(h.canonicalUrl=E),null!==u){let e=u[1],t=u[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,c.fillLazyItemsTillLeafWithHead)(b,y,void 0,r,u,d,void 0),h.prefetchCache=new Map}await (0,p.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:_,updatedCache:y,includeNextUrl:v,canonicalUrl:h.canonicalUrl||e.canonicalUrl}),h.cache=y,h.patchedTree=_,m=_}return(0,u.handleMutable)(e,h)},()=>e)}n(593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9289:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return g},PageNotFoundError:function(){return m},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return u},getLocationOrigin:function(){return i},getURL:function(){return l},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),a=0;a<r;a++)o[a]=arguments[a];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function i(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function l(){let{href:e}=window.location,t=i();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&c(n))return r;if(!r)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class g extends Error{}class m extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},9435:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let r=n(642);function o(e){return void 0!==e}function a(e,t){var n,a;let i=null==(n=t.shouldScroll)||n,l=e.nextUrl;if(o(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?l=n:l||(l=e.canonicalUrl)}return{canonicalUrl:o(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:o(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:o(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:o(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!i&&(!!o(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:i?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:i?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:o(t.patchedTree)?t.patchedTree:e.tree,nextUrl:l}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9651:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return s}});let r=n(7391),o=n(6770),a=n(2030),i=n(5232),l=n(6928),u=n(9435),c=n(9752);function s(e,t){let{serverResponse:{flightData:n,canonicalUrl:s},navigatedAt:f}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof n)return(0,i.handleExternalUrl)(e,d,n,e.pushRef.pendingPush);let p=e.tree,h=e.cache;for(let t of n){let{segmentPath:n,tree:u}=t,g=(0,o.applyRouterStatePatchToTree)(["",...n],p,u,e.canonicalUrl);if(null===g)return e;if((0,a.isNavigatingToNewRootLayout)(p,g))return(0,i.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let m=s?(0,r.createHrefFromUrl)(s):void 0;m&&(d.canonicalUrl=m);let y=(0,c.createEmptyCacheNode)();(0,l.applyFlightData)(f,h,y,t),d.patchedTree=g,d.cache=y,h=y,p=g}return(0,u.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9656:(e,t,n)=>{"use strict";n.r(t),n.d(t,{_:()=>o});var r=0;function o(e){return"__private_"+r+++"_"+e}},9707:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return f},handleAliasedPrefetchEntry:function(){return s}});let r=n(3913),o=n(9752),a=n(6770),i=n(7391),l=n(3123),u=n(3898),c=n(9435);function s(e,t,n,s,d){let p,h=t.tree,g=t.cache,m=(0,i.createHrefFromUrl)(s);if("string"==typeof n)return!1;for(let t of n){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(t.seedData))continue;let n=t.tree;n=f(n,Object.fromEntries(s.searchParams));let{seedData:i,isRootRender:c,pathToSegment:d}=t,y=["",...d];n=f(n,Object.fromEntries(s.searchParams));let v=(0,a.applyRouterStatePatchToTree)(y,h,n,m),b=(0,o.createEmptyCacheNode)();if(c&&i){let t=i[1];b.loading=i[3],b.rsc=t,function e(t,n,o,a,i){if(0!==Object.keys(a[1]).length)for(let u in a[1]){let c,s=a[1][u],f=s[0],d=(0,l.createRouterCacheKey)(f),p=null!==i&&void 0!==i[2][u]?i[2][u]:null;if(null!==p){let e=p[1],n=p[3];c={lazyData:null,rsc:f.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let h=n.parallelRoutes.get(u);h?h.set(d,c):n.parallelRoutes.set(u,new Map([[d,c]])),e(t,c,o,s,p)}}(e,b,g,n,i)}else b.rsc=g.rsc,b.prefetchRsc=g.prefetchRsc,b.loading=g.loading,b.parallelRoutes=new Map(g.parallelRoutes),(0,u.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,g,t);v&&(h=v,g=b,p=!0)}return!!p&&(d.patchedTree=h,d.cache=g,d.canonicalUrl=m,d.hashFragment=s.hash,(0,c.handleMutable)(t,d))}function f(e,t){let[n,o,...a]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),o,...a];let i={};for(let[e,n]of Object.entries(o))i[e]=f(n,t);return[n,i,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9752:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return S},createPrefetchURL:function(){return O},default:function(){return N},isExternalURL:function(){return T}});let r=n(740),o=n(687),a=r._(n(3210)),i=n(2142),l=n(9154),u=n(7391),c=n(449),s=n(9129),f=r._(n(5656)),d=n(5416),p=n(6127),h=n(7022),g=n(7086),m=n(4397),y=n(9330),v=n(5942),b=n(6736),R=n(642),_=n(2776),E=n(3690),w=n(6875),P=n(7860);n(3406);let x={};function T(e){return e.origin!==window.location.origin}function O(e){let t;if((0,d.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return T(t)?null:t}function j(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,o={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,u.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(o,"",r)):window.history.replaceState(o,"",r)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function S(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function M(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function A(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,o=null!==r?r:n;return(0,a.useDeferredValue)(n,o)}function C(e){let t,{actionQueue:n,assetPrefix:r,globalError:u}=e,d=(0,s.useActionQueue)(n),{canonicalUrl:p}=d,{searchParams:_,pathname:T}=(0,a.useMemo)(()=>{let e=new URL(p,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[p]);(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(x.pendingMpaPath=void 0,(0,s.dispatchAppRouterAction)({type:l.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,P.isRedirectError)(t)){e.preventDefault();let n=(0,w.getURLFromRedirectError)(t);(0,w.getRedirectTypeFromError)(t)===P.RedirectType.push?E.publicAppRouterInstance.push(n,{}):E.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:O}=d;if(O.mpaNavigation){if(x.pendingMpaPath!==p){let e=window.location;O.pendingPush?e.assign(p):e.replace(p),x.pendingMpaPath=p}(0,a.use)(y.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:l.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,o){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=M(t),o&&n(o)),e(t,r,o)},window.history.replaceState=function(e,r,o){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=M(e),o&&n(o)),t(e,r,o)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,a.startTransition)(()=>{(0,E.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:S,tree:C,nextUrl:N,focusAndScrollRef:L}=d,k=(0,a.useMemo)(()=>(0,m.findHeadInCache)(S,C[1]),[S,C]),I=(0,a.useMemo)(()=>(0,R.getSelectedParams)(C),[C]),U=(0,a.useMemo)(()=>({parentTree:C,parentCacheNode:S,parentSegmentPath:null,url:p}),[C,S,p]),F=(0,a.useMemo)(()=>({tree:C,focusAndScrollRef:L,nextUrl:N}),[C,L,N]);if(null!==k){let[e,n]=k;t=(0,o.jsx)(A,{headCacheNode:e},n)}else t=null;let H=(0,o.jsxs)(g.RedirectBoundary,{children:[t,S.rsc,(0,o.jsx)(h.AppRouterAnnouncer,{tree:C})]});return H=(0,o.jsx)(f.ErrorBoundary,{errorComponent:u[0],errorStyles:u[1],children:H}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(j,{appRouterState:d}),(0,o.jsx)(D,{}),(0,o.jsx)(c.PathParamsContext.Provider,{value:I,children:(0,o.jsx)(c.PathnameContext.Provider,{value:T,children:(0,o.jsx)(c.SearchParamsContext.Provider,{value:_,children:(0,o.jsx)(i.GlobalLayoutRouterContext.Provider,{value:F,children:(0,o.jsx)(i.AppRouterContext.Provider,{value:E.publicAppRouterInstance,children:(0,o.jsx)(i.LayoutRouterContext.Provider,{value:U,children:H})})})})})})]})}function N(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:a}=e;return(0,_.useNavFailureHandler)(),(0,o.jsx)(f.ErrorBoundary,{errorComponent:f.default,children:(0,o.jsx)(C,{actionQueue:t,assetPrefix:a,globalError:[n,r]})})}let L=new Set,k=new Set;function D(){let[,e]=a.default.useState(0),t=L.size;return(0,a.useEffect)(()=>{let n=()=>e(e=>e+1);return k.add(n),t!==L.size&&n(),()=>{k.delete(n)}},[t,e]),[...L].map((e,t)=>(0,o.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=L.size;return L.add(e),L.size!==t&&k.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};