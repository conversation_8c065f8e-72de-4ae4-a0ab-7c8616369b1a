{"name": "@arbiter/api", "version": "1.0.0", "private": true, "main": "src/index.ts", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "aws-sdk": "^2.1417.0", "stripe": "^12.14.0", "nodemailer": "^6.9.4", "redis": "^4.6.7", "ws": "^8.13.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "@prisma/client": "^5.1.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.1", "nanoid": "^4.0.2", "zod": "^3.21.4"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.8", "@types/ws": "^8.5.5", "@types/compression": "^1.7.2", "tsx": "^3.12.7", "typescript": "^5.1.6"}}