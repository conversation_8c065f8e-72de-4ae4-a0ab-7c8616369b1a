=== Pay Per Crawl ===
Contributors: paypercrawl
Tags: ai, bot-detection, monetization, revenue, crawling, openai, claude, gemini
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 3.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Turn every AI crawl into revenue! Advanced AI bot detection and monetization platform for WordPress.

== Description ==

**Pay Per Crawl** transforms your WordPress site into a revenue-generating platform by detecting and monetizing AI bot visits. Every time ChatGPT, Claude, Gemini, or other AI systems crawl your content, you earn money!

### 🚀 Key Features

* **Real-time AI Bot Detection** - Detect 30+ AI bot types including GPT, Claude, Gemini, and more
* **Automatic Revenue Tracking** - Track earnings from every bot visit with detailed analytics
* **Professional Dashboard** - Beautiful, real-time dashboard with charts and statistics
* **Advanced Bot Signatures** - Comprehensive database of AI crawlers and their rates
* **PayPerCrawl.tech Integration** - Connect to our platform for enhanced features and payments
* **Zero Configuration** - Works out of the box with intelligent defaults

### 💰 Supported AI Bots

**Premium Tier ($0.10-$0.12 per visit)**
* OpenAI GPTBot
* ChatGPT-User
* Anthropic Claude bots
* ClaudeBot

**Standard Tier ($0.06-$0.08 per visit)**
* Google Gemini/Bard
* Google-Extended
* Meta AI bots
* Microsoft Copilot

**Emerging Tier ($0.03-$0.05 per visit)**
* Perplexity AI
* You.com bots
* ByteDance crawlers
* Yandex AI

### 🎯 How It Works

1. **Install & Activate** - Plugin works immediately upon activation
2. **AI Bots Visit** - When AI systems crawl your content, they're automatically detected
3. **Revenue Tracked** - Each visit is logged with revenue calculations
4. **Get Paid** - Connect to PayPerCrawl.tech for automatic payments

### 📊 Dashboard Features

* **Live Statistics** - Real-time bot counts and revenue tracking
* **Revenue Analytics** - Detailed charts and projections
* **Bot Activity Feed** - See exactly which bots visited and when
* **Setup Wizard** - Easy configuration guide
* **Rate Optimization** - Smart suggestions to maximize earnings

### 🔗 PayPerCrawl.tech Integration

Connect your site to our platform for:
* ⚡ Enhanced bot detection (50+ signatures)
* 💰 Higher revenue rates and optimization
* 📊 Advanced analytics and reporting
* 🛠️ API access for custom integrations
* 💳 Automatic payment processing

== Installation ==

### Automatic Installation

1. Go to your WordPress admin panel
2. Navigate to Plugins > Add New
3. Search for "Pay Per Crawl"
4. Click "Install Now" and then "Activate"
5. Visit the Pay Per Crawl dashboard to see your earnings!

### Manual Installation

1. Download the plugin ZIP file
2. Upload to `/wp-content/plugins/` directory
3. Extract the files
4. Activate the plugin through the 'Plugins' menu
5. Configure settings in Pay Per Crawl > Settings

== Frequently Asked Questions ==

= How much can I earn? =

Earnings depend on your site traffic and AI bot visits. Sites with good SEO and valuable content can earn $50-500+ per month. Premium AI bots like GPT-4 pay up to $0.12 per visit.

= Is this really free? =

Yes! The plugin is completely free. Enhanced features and payment processing are available through PayPerCrawl.tech with transparent pricing.

= Which AI bots are detected? =

We detect 30+ AI bot types including OpenAI GPT, Anthropic Claude, Google Gemini/Bard, Meta AI, Microsoft Copilot, Perplexity, and many more.

= How do I get paid? =

Connect your site to PayPerCrawl.tech for automatic payment processing. We support multiple payment methods including PayPal, Stripe, and bank transfers.

= Will this slow down my site? =

No! Our detection system is optimized for performance and adds less than 1ms to page load times.

= Can I customize the rates? =

Yes! You can set custom rate multipliers and configure which bots to detect in the settings panel.

== Screenshots ==

1. **Professional Dashboard** - Real-time statistics and revenue tracking
2. **Revenue Analytics** - Detailed charts showing earnings over time
3. **Bot Activity Feed** - Live feed of detected AI bot visits
4. **Settings Panel** - Easy configuration with toggle switches
5. **Bot Signatures** - Complete list of detected AI bot types

== Changelog ==

= 3.0.0 =
* Complete rebrand to Pay Per Crawl
* New professional dashboard with real-time updates
* Enhanced bot detection with 30+ signatures
* PayPerCrawl.tech API integration
* Advanced analytics and reporting
* Improved performance and user experience
* Modern UI with responsive design

= 2.0.0 =
* Previous version (CrawlGuard WP Pro)

== Upgrade Notice ==

= 3.0.0 =
Major update with complete rebrand to Pay Per Crawl! Enhanced features, better detection, and professional dashboard. Your existing data will be automatically migrated.

== Support ==

For support, documentation, and advanced features:

* **Website**: https://paypercrawl.tech
* **Documentation**: https://paypercrawl.tech/docs
* **Support**: <EMAIL>
* **API**: https://paypercrawl.tech/api

Join thousands of website owners earning passive revenue from AI bot visits with Pay Per Crawl!
