"use strict";(()=>{var e={};e.id=746,e.ids=[746],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2227:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>b,routeModule:()=>x,serverHooks:()=>v,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>m});var a=t(6559),n=t(8088),o=t(7719),i=t(2190),u=t(5069),p=t(9991),c=t(639),l=t(4250);let d=c.Ik({name:c.Yj().min(2,"Name must be at least 2 characters"),email:c.Yj().email("Invalid email address"),subject:c.Yj().optional(),message:c.Yj().min(10,"Message must be at least 10 characters")});async function m(e){try{let r=await e.json(),t=d.parse(r),s=await u.db.contactSubmission.create({data:{name:t.name,email:t.email,subject:t.subject||"General Inquiry",message:t.message}});try{await (0,p.M)(t.name,t.email,t.subject||"General Inquiry",t.message)}catch(e){console.error("Failed to send contact notification email:",e)}return i.NextResponse.json({message:"Contact form submitted successfully",submissionId:s.id})}catch(e){if(console.error("Contact form submission error:",e),e instanceof l.G)return i.NextResponse.json({error:"Validation failed",details:e.issues},{status:400});return i.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:"app/api/contact/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\contact\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:j,workUnitAsyncStorage:g,serverHooks:v}=x;function b(){return(0,o.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:g})}},2502:e=>{e.exports=import("prettier/plugins/html")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3505:e=>{e.exports=import("prettier/standalone")},4297:e=>{e.exports=require("async_hooks")},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{e.exports=require("crypto")},6330:e=>{e.exports=require("@prisma/client")},7075:e=>{e.exports=require("node:stream")},7910:e=>{e.exports=require("stream")},8354:e=>{e.exports=require("util")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,639,123],()=>t(2227));module.exports=s})();