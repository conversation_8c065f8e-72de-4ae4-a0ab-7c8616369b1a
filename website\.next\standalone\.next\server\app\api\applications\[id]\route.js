(()=>{var e={};e.id=303,e.ids=[303],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1678:(e,r,t)=>{"use strict";t.d(r,{db:()=>n});let s=require("@prisma/client"),n=globalThis.prisma??new s.PrismaClient({log:["query"]})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},9099:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>v,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>u,PATCH:()=>d});var n=t(6559),i=t(8088),a=t(7719),o=t(2190),p=t(1678);async function u(e,{params:r}){try{let e=await p.db.betaApplication.findUnique({where:{id:r.id}});if(!e)return o.NextResponse.json({error:"Application not found"},{status:404});return o.NextResponse.json(e)}catch(e){return console.error("Error fetching application:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e,{params:r}){try{let{status:t,notes:s}=await e.json(),n=await p.db.betaApplication.update({where:{id:r.id},data:{status:t||void 0,notes:s||void 0,updatedAt:new Date}});return o.NextResponse.json(n)}catch(e){return console.error("Error updating application:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/applications/[id]/route",pathname:"/api/applications/[id]",filename:"route",bundlePath:"app/api/applications/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\applications\\[id]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:v}=c;function g(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(9099));module.exports=s})();