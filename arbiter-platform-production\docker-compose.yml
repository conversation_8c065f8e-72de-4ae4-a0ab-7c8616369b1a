version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: arbiter-postgres
    environment:
      POSTGRES_DB: arbiter_platform_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./packages/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - arbiter-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: arbiter-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - arbiter-network

  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: arbiter-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - arbiter-network

  # MinIO (S3-compatible storage)
  minio:
    image: minio/minio:latest
    container_name: arbiter-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - arbiter-network

  # Mailhog (Email testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: arbiter-mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - arbiter-network

  # Prisma Studio (Database GUI)
  prisma-studio:
    image: node:18-alpine
    container_name: arbiter-prisma-studio
    working_dir: /app
    volumes:
      - ./packages/database:/app
      - node_modules:/app/node_modules
    ports:
      - "5555:5555"
    command: sh -c "npm install && npx prisma studio --port 5555 --hostname 0.0.0.0"
    depends_on:
      - postgres
    networks:
      - arbiter-network

  # API Server
  api:
    build:
      context: .
      dockerfile: apps/api/Dockerfile
    container_name: arbiter-api
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/arbiter_platform_dev
      - REDIS_URL=redis://redis:6379
      - ELASTICSEARCH_URL=http://elasticsearch:9200
    ports:
      - "4000:4000"
    volumes:
      - ./apps/api:/app/apps/api
      - ./packages:/app/packages
      - node_modules:/app/node_modules
    depends_on:
      - postgres
      - redis
      - elasticsearch
    networks:
      - arbiter-network

  # Web App
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
    container_name: arbiter-web
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:4000
    ports:
      - "3000:3000"
    volumes:
      - ./apps/web:/app/apps/web
      - ./packages:/app/packages
      - node_modules:/app/node_modules
    depends_on:
      - api
    networks:
      - arbiter-network

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
  minio_data:
  node_modules:

networks:
  arbiter-network:
    driver: bridge
