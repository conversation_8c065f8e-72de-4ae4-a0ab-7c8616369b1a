{"name": "arbiter-platform-enhanced", "version": "2.0.0", "description": "Enhanced Content Licensing Platform for Creators and AI Companies", "main": "index.js", "scripts": {"setup": "node scripts/enhanced-setup.js", "dev": "concurrently \"npm run dev:services\" \"npm run dev:frontend\" \"npm run dev:database\"", "dev:frontend": "cd frontend && npm run dev", "dev:services": "concurrently \"npm run dev:api-gateway\" \"npm run dev:auth\" \"npm run dev:content\" \"npm run dev:payment\" \"npm run dev:analytics\" \"npm run dev:notification\" \"npm run dev:bot-detection\" \"npm run dev:pricing\" \"npm run dev:licensing\"", "dev:database": "docker-compose up -d postgres redis minio elasticsearch", "dev:api-gateway": "cd backend/api-gateway && npm run dev", "dev:auth": "cd backend/services/auth && npm run dev", "dev:content": "cd backend/services/content && npm run dev", "dev:payment": "cd backend/services/payment && npm run dev", "dev:analytics": "cd backend/services/analytics && npm run dev", "dev:notification": "cd backend/services/notification && npm run dev", "dev:bot-detection": "cd backend/services/bot-detection && npm run dev", "dev:pricing": "cd backend/services/pricing && npm run dev", "dev:licensing": "cd backend/services/licensing && npm run dev", "build": "npm run build:frontend && npm run build:services", "build:frontend": "cd frontend && npm run build", "build:services": "concurrently \"npm run build:api-gateway\" \"npm run build:auth\" \"npm run build:content\" \"npm run build:payment\" \"npm run build:analytics\" \"npm run build:notification\" \"npm run build:bot-detection\" \"npm run build:pricing\" \"npm run build:licensing\"", "test": "npm run test:unit && npm run test:integration", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "cypress run", "db:migrate": "cd backend && npx prisma migrate dev", "db:seed": "cd backend && npx prisma db seed", "db:reset": "cd backend && npx prisma migrate reset --force", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "prototype:demo": "node scripts/demo-data.js && npm run dev", "prototype:reset": "npm run db:reset && npm run db:seed", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "type-check": "tsc --noEmit"}, "dependencies": {"@prisma/client": "^5.20.0", "express": "^4.21.0", "express-rate-limit": "^7.4.0", "helmet": "^8.0.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.15.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.13.3", "multer": "^1.4.5-lts.1", "sharp": "^0.33.5", "stripe": "^17.1.0", "nodemailer": "^6.9.16", "twilio": "^5.3.2", "redis": "^4.7.0", "bull": "^4.16.3", "axios": "^1.7.7", "socket.io": "^4.8.0", "ws": "^8.18.0", "uuid": "^10.0.0", "moment": "^2.30.1", "lodash": "^4.17.21", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "@elastic/elasticsearch": "^8.15.0", "minio": "^8.0.1", "pdf-parse": "^1.1.1", "fluent-ffmpeg": "^2.1.3", "node-cron": "^3.0.3"}, "devDependencies": {"@types/node": "^22.7.4", "@types/express": "^5.0.0", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.16", "@types/uuid": "^10.0.0", "@types/lodash": "^4.17.9", "@types/ws": "^8.5.12", "@types/fluent-ffmpeg": "^2.1.26", "@types/node-cron": "^3.0.11", "typescript": "^5.6.2", "ts-node": "^10.9.2", "nodemon": "^3.1.7", "concurrently": "^9.0.1", "prisma": "^5.20.0", "jest": "^29.7.0", "@types/jest": "^29.5.13", "ts-jest": "^29.2.5", "cypress": "^13.15.0", "eslint": "^9.11.1", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "prettier": "^3.3.3", "husky": "^9.1.6", "lint-staged": "^15.2.10"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/ImadDev5/arbiter-platform.git"}, "author": "Arbiter Platform Team", "license": "MIT", "keywords": ["content-licensing", "ai-training", "creator-economy", "microservices", "typescript", "react", "node.js"]}