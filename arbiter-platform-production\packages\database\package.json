{"name": "@arbiter/database", "version": "1.0.0", "private": true, "main": "index.ts", "scripts": {"db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^5.1.1", "bcryptjs": "^2.4.3", "nanoid": "^4.0.2"}, "devDependencies": {"prisma": "^5.1.1", "tsx": "^3.12.7", "@types/bcryptjs": "^2.4.2"}}