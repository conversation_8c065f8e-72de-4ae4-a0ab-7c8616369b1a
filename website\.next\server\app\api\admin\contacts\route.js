(()=>{var e={};e.id=889,e.ids=[889],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1678:(e,t,r)=>{"use strict";r.d(t,{db:()=>n});let s=require("@prisma/client"),n=globalThis.prisma??new s.PrismaClient({log:["query"]})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5907:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>p,PATCH:()=>d});var n=r(6559),a=r(8088),o=r(7719),i=r(2190),u=r(1678);function c(e){let t=e.headers.get("authorization");return t?.replace("Bearer ","")===process.env.ADMIN_API_KEY}async function p(e){try{if(!c(e))return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),r=t.get("status"),s=parseInt(t.get("page")||"1"),n=parseInt(t.get("limit")||"10"),a=(s-1)*n,o=r?{status:r}:{},[p,d]=await Promise.all([u.db.contactSubmission.findMany({where:o,orderBy:{createdAt:"desc"},skip:a,take:n}),u.db.contactSubmission.count({where:o})]);return i.NextResponse.json({contactSubmissions:p,pagination:{page:s,limit:n,total:d,pages:Math.ceil(d/n)}})}catch(e){return console.error("Error fetching contact submissions:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{if(!c(e))return i.NextResponse.json({error:"Unauthorized"},{status:401});let{contactId:t,status:r}=await e.json(),s=await u.db.contactSubmission.update({where:{id:t},data:{status:r||void 0,updatedAt:new Date}});return i.NextResponse.json(s)}catch(e){return console.error("Error updating contact submission:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/contacts/route",pathname:"/api/admin/contacts",filename:"route",bundlePath:"app/api/admin/contacts/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\admin\\contacts\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:g}=l;function h(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(5907));module.exports=s})();