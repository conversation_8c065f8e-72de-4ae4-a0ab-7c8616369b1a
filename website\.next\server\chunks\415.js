exports.id=415,exports.ids=[415],exports.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(1658);let s=async e=>[{type:"image/x-icon",sizes:"128x128",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},1949:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},3389:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},3701:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\components\\theme-provider.tsx","ThemeProvider")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>l});var a=r(7413),s=r(2376),n=r.n(s),o=r(8726),i=r.n(o);r(1135);var d=r(9737),c=r(3701);let l={title:"PayPerCrawl - AI Content Monetization Platform",description:"Turn AI bot traffic into revenue. PayPerCrawl detects AI bots crawling your content and converts them into paying customers. Built for WordPress with enterprise-grade security.",keywords:["AI monetization","WordPress","content protection","bot detection","revenue generation","AI training data"],authors:[{name:"PayPerCrawl Team"}],openGraph:{title:"PayPerCrawl - AI Content Monetization Platform",description:"Turn AI bot traffic into revenue with advanced bot detection and monetization",url:"https://paypercrawl.tech",siteName:"PayPerCrawl",type:"website"},twitter:{card:"summary_large_image",title:"PayPerCrawl - AI Content Monetization Platform",description:"Turn AI bot traffic into revenue with advanced bot detection and monetization"}};function u({children:e}){return(0,a.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,a.jsxs)("body",{className:`${n().variable} ${i().variable} antialiased bg-background text-foreground`,children:[(0,a.jsx)(c.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e}),(0,a.jsx)(d.Toaster,{})]})})}},4493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>o});var a=r(687);r(3210);var s=r(4780);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},4780:(e,t,r)=>{"use strict";r.d(t,{Y:()=>o,cn:()=>n});var a=r(9384),s=r(2348);function n(...e){return(0,s.QP)((0,a.$)(e))}function o(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(e)}},4947:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>h});var a=r(687),s=r(9867),n=r(3210),o=r(7313),i=r(4224),d=r(1860),c=r(4780);let l=o.Kq,u=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.LM,{ref:r,className:(0,c.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));u.displayName=o.LM.displayName;let v=(0,i.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),m=n.forwardRef(({className:e,variant:t,...r},s)=>(0,a.jsx)(o.bL,{ref:s,className:(0,c.cn)(v({variant:t}),e),...r}));m.displayName=o.bL.displayName,n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.rc,{ref:r,className:(0,c.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=o.rc.displayName;let f=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.bm,{ref:r,className:(0,c.cn)("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})}));f.displayName=o.bm.displayName;let p=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.hE,{ref:r,className:(0,c.cn)("text-sm font-semibold [&+div]:text-xs",e),...t}));p.displayName=o.hE.displayName;let g=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(o.VY,{ref:r,className:(0,c.cn)("text-sm opacity-90",e),...t}));function h(){let{toasts:e}=(0,s.dj)();return(0,a.jsxs)(l,{children:[e.map(function({id:e,title:t,description:r,action:s,...n}){return(0,a.jsxs)(m,{...n,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[t&&(0,a.jsx)(p,{children:t}),r&&(0,a.jsx)(g,{children:r})]}),s,(0,a.jsx)(f,{})]},e)}),(0,a.jsx)(u,{})]})}g.displayName=o.VY.displayName},5010:(e,t,r)=>{Promise.resolve().then(r.bind(r,3701)),Promise.resolve().then(r.bind(r,9737))},6834:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var a=r(687);r(3210);var s=r(8730),n=r(4224),o=r(4780);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...n}){let d=r?s.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...n})}},6871:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var a=r(687);r(3210);var s=r(218);function n({children:e,...t}){return(0,a.jsx)(s.N,{...t,children:e})}},8570:(e,t,r)=>{Promise.resolve().then(r.bind(r,6871)),Promise.resolve().then(r.bind(r,4947))},9523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(687);r(3210);var s=r(8730),n=r(4224),o=r(4780);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:n=!1,...d}){let c=n?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:r,className:e})),...d})}},9649:(e,t,r)=>{"use strict";r.d(t,{c:()=>f});var a=r(687);r(3210);var s=r(1134),n=r(363),o=r(218),i=r(9523),d=r(3931),c=r(4780);function l({...e}){return(0,a.jsx)(d.bL,{"data-slot":"dropdown-menu",...e})}function u({...e}){return(0,a.jsx)(d.l9,{"data-slot":"dropdown-menu-trigger",...e})}function v({className:e,sideOffset:t=4,...r}){return(0,a.jsx)(d.ZL,{children:(0,a.jsx)(d.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function m({className:e,inset:t,variant:r="default",...s}){return(0,a.jsx)(d.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}function f(){let{setTheme:e}=(0,o.D)();return(0,a.jsxs)(l,{children:[(0,a.jsx)(u,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"outline",size:"icon",children:[(0,a.jsx)(s.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,a.jsx)(n.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,a.jsxs)(v,{align:"end",children:[(0,a.jsx)(m,{onClick:()=>e("light"),children:"Light"}),(0,a.jsx)(m,{onClick:()=>e("dark"),children:"Dark"}),(0,a.jsx)(m,{onClick:()=>e("system"),children:"System"})]})]})}},9737:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>a});let a=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\components\\ui\\toaster.tsx","Toaster")},9867:(e,t,r)=>{"use strict";r.d(t,{dj:()=>v});var a=r(3210);let s=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?o(r):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],c={toasts:[]};function l(e){c=i(c,e),d.forEach(e=>{e(c)})}function u({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>l({type:"DISMISS_TOAST",toastId:t});return l({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function v(){let[e,t]=a.useState(c);return a.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}}};