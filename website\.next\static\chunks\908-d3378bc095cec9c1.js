"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[908],{1285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(2115),i=n(2712),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},2098:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(2115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:a()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2664:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let r=n(9991),o=n(7102);function i(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},2757:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return l},urlObjectKeys:function(){return a}});let r=n(6966)._(n(8859)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:n}=e,i=e.protocol||"",a=e.pathname||"",l=e.hash||"",u=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:n&&(c=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(c+=":"+e.port)),u&&"object"==typeof u&&(u=String(r.urlQueryToSearchParams(u)));let s=e.search||u&&"?"+u||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==c?(c="//"+(c||""),a&&"/"!==a[0]&&(a="/"+a)):c||(c=""),l&&"#"!==l[0]&&(l="#"+l),s&&"?"!==s[0]&&(s="?"+s),""+i+c+(a=a.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+l}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return i(e)}},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},3509:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3795:(e,t,n)=>{n.d(t,{A:()=>V});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(2115)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var f="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,d=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=i({async:!0,ssr:!1},e),a}(),m=function(){},v=l.forwardRef(function(e,t){var n,r,o,u,c=l.useRef(null),p=l.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,R=e.shards,C=e.sideCar,A=e.noRelative,S=e.noIsolation,M=e.inert,P=e.allowPinchZoom,T=e.as,k=e.gapMode,j=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,f(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}d.set(u,n)},[n]),u),O=i(i({},j),v);return l.createElement(l.Fragment,null,E&&l.createElement(C,{sideCar:h,removeScrollBar:x,shards:R,noRelative:A,noIsolation:S,inert:M,setCallbacks:g,allowPinchZoom:!!P,lockRef:c,gapMode:k}),y?l.cloneElement(l.Children.only(w),i(i({},O),{ref:L})):l.createElement(void 0===T?"div":T,i({},O,{className:b,ref:L}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},R=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=R(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},A=b(),S="data-scroll-locked",M=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},P=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},T=function(){l.useEffect(function(){return document.body.setAttribute(S,(P()+1).toString()),function(){var e=P()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},k=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;T();var i=l.useMemo(function(){return C(o)},[o]);return l.createElement(A,{styles:M(i,!t,o,n?"":"!important")})},j=!1;if("undefined"!=typeof window)try{var L=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",L,L),window.removeEventListener("test",L,L)}catch(e){j=!1}var O=!!j&&{passive:!1},D=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},N=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),_(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},_=function(e,t){return"v"===e?D(t,"overflowY"):D(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,f=l>0,d=0,p=0;do{if(!u)break;var h=I(e,u),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&_(e,u)&&(d+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&l>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},K=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},W=0,G=[];let H=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(W++)[0],i=l.useState(b)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=K(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=N(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=N(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(G.length&&G[G.length-1]===i){var n="deltaY"in e?B(e):K(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=l.useCallback(function(e){n.current=K(e),r.current=void 0},[]),d=l.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,K(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return G.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",c,O),document.addEventListener("touchmove",c,O),document.addEventListener("touchstart",f,O),function(){G=G.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,O),document.removeEventListener("touchmove",c,O),document.removeEventListener("touchstart",f,O)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(k,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),g);var z=l.forwardRef(function(e,t){return l.createElement(v,i({},e,{ref:t,sideCar:H}))});z.classNames=v.classNames;let V=z},4315:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(2115);n(5155);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},6654:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(2115);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=i(e,r)),t&&(o.current=i(t,r))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return v},useLinkStatus:function(){return y}});let r=n(6966),o=n(5155),i=r._(n(2115)),a=n(2757),l=n(5227),u=n(9818),c=n(6654),s=n(9991),f=n(5929);n(3230);let d=n(4930),p=n(2664),h=n(6634);function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function v(e){let t,n,r,[a,v]=(0,i.useOptimistic)(d.IDLE_LINK_STATUS),y=(0,i.useRef)(null),{href:w,as:b,children:x,prefetch:E=null,passHref:R,replace:C,shallow:A,scroll:S,onClick:M,onMouseEnter:P,onTouchStart:T,legacyBehavior:k=!1,onNavigate:j,ref:L,unstable_dynamicOnHover:O,...D}=e;t=x,k&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let N=i.default.useContext(l.AppRouterContext),_=!1!==E,I=null===E?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:F,as:K}=i.default.useMemo(()=>{let e=m(w);return{href:e,as:b?m(b):e}},[w,b]);k&&(n=i.default.Children.only(t));let B=k?n&&"object"==typeof n&&n.ref:L,U=i.default.useCallback(e=>(null!==N&&(y.current=(0,d.mountLinkInstance)(e,F,N,I,_,v)),()=>{y.current&&((0,d.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,d.unmountPrefetchableInstance)(e)}),[_,F,N,I,v]),W={ref:(0,c.useMergedRef)(U,B),onClick(e){k||"function"!=typeof M||M(e),k&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),N&&(e.defaultPrevented||function(e,t,n,r,o,a,l){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),i.default.startTransition(()=>{if(l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(n||t,o?"replace":"push",null==a||a,r.current)})}}(e,F,K,y,C,S,j))},onMouseEnter(e){k||"function"!=typeof P||P(e),k&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),N&&_&&(0,d.onNavigationIntent)(e.currentTarget,!0===O)},onTouchStart:function(e){k||"function"!=typeof T||T(e),k&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),N&&_&&(0,d.onNavigationIntent)(e.currentTarget,!0===O)}};return(0,s.isAbsoluteUrl)(K)?W.href=K:k&&!R&&("a"!==n.type||"href"in n.props)||(W.href=(0,f.addBasePath)(K)),r=k?i.default.cloneElement(n,W):(0,o.jsx)("a",{...D,...W,children:t}),(0,o.jsx)(g.Provider,{value:a,children:r})}n(3180);let g=(0,i.createContext)(d.IDLE_LINK_STATUS),y=()=>(0,i.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7900:(e,t,n)=>{n.d(t,{n:()=>f});var r=n(2115),o=n(6101),i=n(3655),a=n(9033),l=n(5155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,a.c)(v),E=(0,a.c)(g),R=r.useRef(null),C=(0,o.s)(t,e=>b(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(A.paused||!w)return;let t=e.target;w.contains(t)?R.current=t:h(R.current,{select:!0})},t=function(e){if(A.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(R.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,w,A.paused]),r.useEffect(()=>{if(w){m.add(A);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,E),m.remove(A)},0)}}},[w,x,E,A]);let S=r.useCallback(e=>{if(!n&&!f||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,A.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:S})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null==(n=(e=v(e,t))[0])||n.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},8168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],f=[],d=new Set,p=new Set(c),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,l),s.set(e,u),f.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),d.clear(),l++,function(){f.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},8698:(e,t,n)=>{n.d(t,{UC:()=>eV,q7:()=>eX,ZL:()=>ez,bL:()=>eG,l9:()=>eH});var r=n(2115),o=n(5185),i=n(6101),a=n(6081),l=n(5845),u=n(3655),c=n(7328),s=n(4315),f=n(9178),d=n(2293),p=n(7900),h=n(1285),m=n(8795),v=n(4378),g=n(8905),y=n(9196),w=n(9708),b=n(9033),x=n(8168),E=n(3795),R=n(5155),C=["Enter"," "],A=["ArrowUp","PageDown","End"],S=["ArrowDown","PageUp","Home",...A],M={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},P={ltr:["ArrowLeft"],rtl:["ArrowRight"]},T="Menu",[k,j,L]=(0,c.N)(T),[O,D]=(0,a.A)(T,[L,m.Bk,y.RG]),N=(0,m.Bk)(),_=(0,y.RG)(),[I,F]=O(T),[K,B]=O(T),U=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=N(t),[c,f]=r.useState(null),d=r.useRef(!1),p=(0,b.c)(a),h=(0,s.jH)(i);return r.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,R.jsx)(m.bL,{...u,children:(0,R.jsx)(I,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:f,children:(0,R.jsx)(K,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:d,dir:h,modal:l,children:o})})})};U.displayName=T;var W=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=N(n);return(0,R.jsx)(m.Mz,{...o,...r,ref:t})});W.displayName="MenuAnchor";var G="MenuPortal",[H,z]=O(G,{forceMount:void 0}),V=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=F(G,t);return(0,R.jsx)(H,{scope:t,forceMount:n,children:(0,R.jsx)(g.C,{present:n||i.open,children:(0,R.jsx)(v.Z,{asChild:!0,container:o,children:r})})})};V.displayName=G;var X="MenuContent",[q,Y]=O(X),Z=r.forwardRef((e,t)=>{let n=z(X,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=F(X,e.__scopeMenu),a=B(X,e.__scopeMenu);return(0,R.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(g.C,{present:r||i.open,children:(0,R.jsx)(k.Slot,{scope:e.__scopeMenu,children:a.modal?(0,R.jsx)($,{...o,ref:t}):(0,R.jsx)(Q,{...o,ref:t})})})})}),$=r.forwardRef((e,t)=>{let n=F(X,e.__scopeMenu),a=r.useRef(null),l=(0,i.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,x.Eq)(e)},[]),(0,R.jsx)(ee,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=r.forwardRef((e,t)=>{let n=F(X,e.__scopeMenu);return(0,R.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),J=(0,w.TL)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:x,disableOutsideScroll:C,...M}=e,P=F(X,n),T=B(X,n),k=N(n),L=_(n),O=j(n),[D,I]=r.useState(null),K=r.useRef(null),U=(0,i.s)(t,K,P.onContentChange),W=r.useRef(0),G=r.useRef(""),H=r.useRef(0),z=r.useRef(null),V=r.useRef("right"),Y=r.useRef(0),Z=C?E.A:r.Fragment,$=e=>{var t,n;let r=G.current+e,o=O().filter(e=>!e.disabled),i=document.activeElement,a=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,a),u=null==(n=o.find(e=>e.textValue===l))?void 0:n.ref.current;!function e(t){G.current=t,window.clearTimeout(W.current),""!==t&&(W.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())};r.useEffect(()=>()=>window.clearTimeout(W.current),[]),(0,d.Oh)();let Q=r.useCallback(e=>{var t,n;return V.current===(null==(t=z.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,f=l.y;c>r!=f>r&&n<(s-u)*(r-c)/(f-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=z.current)?void 0:n.area)},[]);return(0,R.jsx)(q,{scope:n,searchRef:G,onItemEnter:r.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:r.useCallback(e=>{var t;Q(e)||(null==(t=K.current)||t.focus(),I(null))},[Q]),onTriggerLeave:r.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:H,onPointerGraceIntentChange:r.useCallback(e=>{z.current=e},[]),children:(0,R.jsx)(Z,{...C?{as:J,allowPinchZoom:!0}:void 0,children:(0,R.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{var t;e.preventDefault(),null==(t=K.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,R.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:x,children:(0,R.jsx)(y.bL,{asChild:!0,...L,dir:T.dir,orientation:"vertical",loop:a,currentTabStopId:D,onCurrentTabStopIdChange:I,onEntryFocus:(0,o.m)(h,e=>{T.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,R.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eS(P.open),"data-radix-menu-content":"",dir:T.dir,...k,...M,ref:U,style:{outline:"none",...M.style},onKeyDown:(0,o.m)(M.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&$(e.key));let o=K.current;if(e.target!==o||!S.includes(e.key))return;e.preventDefault();let i=O().filter(e=>!e.disabled).map(e=>e.ref.current);A.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(W.current),G.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{let t=e.target,n=Y.current!==e.clientX;e.currentTarget.contains(t)&&n&&(V.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});Z.displayName=X;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,R.jsx)(u.sG.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,R.jsx)(u.sG.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",eo="menu.itemSelect",ei=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...l}=e,c=r.useRef(null),s=B(er,e.__scopeMenu),f=Y(er,e.__scopeMenu),d=(0,i.s)(t,c),p=r.useRef(!1);return(0,R.jsx)(ea,{...l,ref:d,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==a?void 0:a(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==f.searchRef.current;n||t&&" "===e.key||C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ei.displayName=er;var ea=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:l,...c}=e,s=Y(er,n),f=_(n),d=r.useRef(null),p=(0,i.s)(t,d),[h,m]=r.useState(!1),[v,g]=r.useState("");return r.useEffect(()=>{let e=d.current;if(e){var t;g((null!=(t=e.textContent)?t:"").trim())}},[c.children]),(0,R.jsx)(k.ItemSlot,{scope:n,disabled:a,textValue:null!=l?l:v,children:(0,R.jsx)(y.q7,{asChild:!0,...f,focusable:!a,children:(0,R.jsx)(u.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eT(e=>{a?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),el=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,R.jsx)(em,{scope:e.__scopeMenu,checked:n,children:(0,R.jsx)(ei,{role:"menuitemcheckbox","aria-checked":eM(n)?"mixed":n,...i,ref:t,"data-state":eP(n),onSelect:(0,o.m)(i.onSelect,()=>null==r?void 0:r(!!eM(n)||!n),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[ec,es]=O(eu,{value:void 0,onValueChange:()=>{}}),ef=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,b.c)(r);return(0,R.jsx)(ec,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,R.jsx)(et,{...o,ref:t})})});ef.displayName=eu;var ed="MenuRadioItem",ep=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=es(ed,e.__scopeMenu),a=n===i.value;return(0,R.jsx)(em,{scope:e.__scopeMenu,checked:a,children:(0,R.jsx)(ei,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":eP(a),onSelect:(0,o.m)(r.onSelect,()=>{var e;return null==(e=i.onValueChange)?void 0:e.call(i,n)},{checkForDefaultPrevented:!1})})})});ep.displayName=ed;var eh="MenuItemIndicator",[em,ev]=O(eh,{checked:!1}),eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=ev(eh,n);return(0,R.jsx)(g.C,{present:r||eM(i.checked)||!0===i.checked,children:(0,R.jsx)(u.sG.span,{...o,ref:t,"data-state":eP(i.checked)})})});eg.displayName=eh;var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,R.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ey.displayName="MenuSeparator";var ew=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=N(n);return(0,R.jsx)(m.i3,{...o,...r,ref:t})});ew.displayName="MenuArrow";var[eb,ex]=O("MenuSub"),eE="MenuSubTrigger",eR=r.forwardRef((e,t)=>{let n=F(eE,e.__scopeMenu),a=B(eE,e.__scopeMenu),l=ex(eE,e.__scopeMenu),u=Y(eE,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:f}=u,d={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),f(null)}},[s,f]),(0,R.jsx)(W,{asChild:!0,...d,children:(0,R.jsx)(ea,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eS(n.open),...e,ref:(0,i.t)(t,l.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eT(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eT(e=>{var t,r;p();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,i="right"===t,a=o[i?"left":"right"],l=o[i?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:a,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;if(!e.disabled&&(!r||" "!==t.key)&&M[a.dir].includes(t.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),t.preventDefault()}})})})});eR.displayName=eE;var eC="MenuSubContent",eA=r.forwardRef((e,t)=>{let n=z(X,e.__scopeMenu),{forceMount:a=n.forceMount,...l}=e,u=F(X,e.__scopeMenu),c=B(X,e.__scopeMenu),s=ex(eC,e.__scopeMenu),f=r.useRef(null),d=(0,i.s)(t,f);return(0,R.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(g.C,{present:a||u.open,children:(0,R.jsx)(k.Slot,{scope:e.__scopeMenu,children:(0,R.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:d,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;c.isUsingKeyboardRef.current&&(null==(t=f.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=P[c.dir].includes(e.key);if(t&&n){var r;u.onOpenChange(!1),null==(r=s.trigger)||r.focus(),e.preventDefault()}})})})})})});function eS(e){return e?"open":"closed"}function eM(e){return"indeterminate"===e}function eP(e){return eM(e)?"indeterminate":e?"checked":"unchecked"}function eT(e){return t=>"mouse"===t.pointerType?e(t):void 0}eA.displayName=eC;var ek="DropdownMenu",[ej,eL]=(0,a.A)(ek,[D]),eO=D(),[eD,eN]=ej(ek),e_=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:c=!0}=e,s=eO(t),f=r.useRef(null),[d,p]=(0,l.i)({prop:i,defaultProp:null!=a&&a,onChange:u,caller:ek});return(0,R.jsx)(eD,{scope:t,triggerId:(0,h.B)(),triggerRef:f,contentId:(0,h.B)(),open:d,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,R.jsx)(U,{...s,open:d,onOpenChange:p,dir:o,modal:c,children:n})})};e_.displayName=ek;var eI="DropdownMenuTrigger",eF=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,l=eN(eI,n),c=eO(n);return(0,R.jsx)(W,{asChild:!0,...c,children:(0,R.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eI;var eK=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eO(t);return(0,R.jsx)(V,{...r,...n})};eK.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eU=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eN(eB,n),l=eO(n),u=r.useRef(!1);return(0,R.jsx)(Z,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;u.current||null==(t=a.triggerRef.current)||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eU.displayName=eB,r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,R.jsx)(et,{...o,...r,ref:t})}).displayName="DropdownMenuGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,R.jsx)(en,{...o,...r,ref:t})}).displayName="DropdownMenuLabel";var eW=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,R.jsx)(ei,{...o,...r,ref:t})});eW.displayName="DropdownMenuItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,R.jsx)(el,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,R.jsx)(ef,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,R.jsx)(ep,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,R.jsx)(eg,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,R.jsx)(ey,{...o,...r,ref:t})}).displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,R.jsx)(ew,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,R.jsx)(eR,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eO(n);return(0,R.jsx)(eA,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eG=e_,eH=eF,ez=eK,eV=eU,eX=eW},8795:(e,t,n)=>{n.d(t,{Mz:()=>e7,i3:()=>tt,UC:()=>te,bL:()=>e4,Bk:()=>eV});var r=n(2115);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>f[e])}let b=["left","right"],x=["right","left"],E=["top","bottom"],R=["bottom","top"];function C(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function A(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function S(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function M(e,t,n){let r,{reference:o,floating:i}=e,a=y(t),l=m(y(t)),u=v(l),c=p(t),s="y"===a,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,g=o[u]/2-i[u]/2;switch(c){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=g*(n&&s?-1:1);break;case"end":r[l]+=g*(n&&s?-1:1)}return r}let P=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=M(c,r,u),d=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=M(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function T(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=A(h),v=l[p?"floating"===f?"reference":"floating":f],g=S(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),b=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},x=S(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-x.top+m.top)/b.y,bottom:(x.bottom-g.bottom+m.bottom)/b.y,left:(g.left-x.left+m.left)/b.x,right:(x.right-g.right+m.right)/b.x}}function k(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function j(e){return o.some(t=>e[t]>=0)}let L=new Set(["left","top"]);async function O(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),u="y"===y(n),c=L.has(a)?-1:1,s=i&&u?-1:1,f=d(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:g}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&"number"==typeof g&&(v="end"===l?-1*g:g),u?{x:v*s,y:m*c}:{x:m*c,y:v*s}}function D(){return"undefined"!=typeof window}function N(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function _(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function I(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!D()&&(e instanceof Node||e instanceof _(e).Node)}function K(e){return!!D()&&(e instanceof Element||e instanceof _(e).Element)}function B(e){return!!D()&&(e instanceof HTMLElement||e instanceof _(e).HTMLElement)}function U(e){return!!D()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof _(e).ShadowRoot)}let W=new Set(["inline","contents"]);function G(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!W.has(o)}let H=new Set(["table","td","th"]),z=[":popover-open",":modal"];function V(e){return z.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let X=["transform","translate","scale","rotate","perspective"],q=["transform","translate","scale","rotate","perspective","filter"],Y=["paint","layout","strict","content"];function Z(e){let t=$(),n=K(e)?ee(e):e;return X.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||q.some(e=>(n.willChange||"").includes(e))||Y.some(e=>(n.contain||"").includes(e))}function $(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let Q=new Set(["html","body","#document"]);function J(e){return Q.has(N(e))}function ee(e){return _(e).getComputedStyle(e)}function et(e){return K(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===N(e))return e;let t=e.assignedSlot||e.parentNode||U(e)&&e.host||I(e);return U(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return J(n)?t.ownerDocument?t.ownerDocument.body:t.body:B(n)&&G(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=_(o);if(i){let e=eo(a);return t.concat(a,a.visualViewport||[],G(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=B(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function ea(e){return K(e)?e:e.contextElement}function el(e){let t=ea(e);if(!B(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ei(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let eu=c(0);function ec(e){let t=_(e);return $()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function es(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=ea(e),l=c(1);t&&(r?K(r)&&(l=el(r)):l=el(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===_(a))&&o)?ec(a):c(0),s=(i.left+u.x)/l.x,f=(i.top+u.y)/l.y,d=i.width/l.x,p=i.height/l.y;if(a){let e=_(a),t=r&&K(r)?_(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=el(o),t=o.getBoundingClientRect(),r=ee(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=a,o=eo(n=_(o))}}return S({width:d,height:p,x:s,y:f})}function ef(e,t){let n=et(e).scrollLeft;return t?t.left+n:es(I(e)).left+n}function ed(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ef(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=_(e),r=I(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=$();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=I(e),n=et(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ef(e),u=-n.scrollTop;return"rtl"===ee(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(I(e));else if(K(t))r=function(e,t){let n=es(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=B(e)?el(e):c(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=ec(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return S(r)}function em(e){return"static"===ee(e).position}function ev(e,t){if(!B(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return I(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=_(e);if(V(e))return r;if(!B(e)){let t=en(e);for(;t&&!J(t);){if(K(t)&&!em(t))return t;t=en(t)}return r}let o=ev(e,t);for(;o&&(n=o,H.has(N(n)))&&em(o);)o=ev(o,t);return o&&J(o)&&em(o)&&!Z(o)?r:o||function(e){let t=en(e);for(;B(t)&&!J(t);){if(Z(t))return t;if(V(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=B(t),o=I(t),i="fixed"===n,a=es(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i)if(("body"!==N(t)||G(o))&&(l=et(t)),r){let e=es(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ef(o));i&&!r&&o&&(u.x=ef(o));let s=!o||r||i?c(0):ed(o,l);return{x:a.left+l.scrollLeft-u.x-s.x,y:a.top+l.scrollTop-u.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=I(r),l=!!t&&V(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),f=c(0),d=B(r);if((d||!d&&!i)&&(("body"!==N(r)||G(a))&&(u=et(r)),B(r))){let e=es(r);s=el(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!a||d||i?c(0):ed(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-u.scrollTop*s.y+f.y+p.y}},getDocumentElement:I,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?V(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>K(e)&&"body"!==N(e)),o=null,i="fixed"===ee(e).position,a=i?en(e):e;for(;K(a)&&!J(a);){let t=ee(a),n=Z(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||G(a)&&!n&&function e(t,n){let r=en(t);return!(r===n||!K(r)||J(r))&&("fixed"===ee(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=en(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],c=l.reduce((e,n)=>{let r=eh(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},eh(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ei(e);return{width:t,height:n}},getScale:el,isElement:K,isRTL:function(e){return"rtl"===ee(e).direction}};function eb(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ex=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:c,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let g=A(p),w={x:n,y:r},b=m(y(o)),x=v(b),E=await u.getDimensions(f),R="y"===b,C=R?"clientHeight":"clientWidth",S=l.reference[x]+l.reference[b]-w[b]-l.floating[x],M=w[b]-l.reference[b],P=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),T=P?P[C]:0;T&&await (null==u.isElement?void 0:u.isElement(P))||(T=c.floating[C]||l.floating[x]);let k=T/2-E[x]/2-1,j=i(g[R?"top":"left"],k),L=i(g[R?"bottom":"right"],k),O=T-E[x]-L,D=T/2-E[x]/2+(S/2-M/2),N=a(j,i(D,O)),_=!s.arrow&&null!=h(o)&&D!==N&&l.reference[x]/2-(D<j?j:L)-E[x]/2<0,I=_?D<j?D-j:D-O:0;return{[b]:w[b]+I,data:{[b]:N,centerOffset:D-N-I,..._&&{alignmentOffset:I}},reset:_}}}),eE=(e,t,n)=>{let r=new Map,o={platform:ew,...n},i={...o.platform,_c:r};return P(e,t,{...o,platform:i})};var eR=n(7650),eC="undefined"!=typeof document?r.useLayoutEffect:function(){};function eA(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eA(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eA(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eS(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eM(e,t){let n=eS(e);return Math.round(t*n)/n}function eP(e){let t=r.useRef(e);return eC(()=>{t.current=e}),t}let eT=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ex({element:n.current,padding:r}).fn(t):{}:n?ex({element:n,padding:r}).fn(t):{}}}),ek=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await O(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),f={x:n,y:r},h=await T(t,s),v=y(p(o)),g=m(v),w=f[g],b=f[v];if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,i(b,r))}let x=c.fn({...t,[g]:w,[v]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[g]:l,[v]:u}}}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=d(e,t),s={x:n,y:r},f=y(o),h=m(f),v=s[h],g=s[f],w=d(l,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===h?"width":"height",t=L.has(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[h]:v,[f]:g}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:f,elements:g}=t,{mainAxis:A=!0,crossAxis:S=!0,fallbackPlacements:M,fallbackStrategy:P="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:j=!0,...L}=d(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let O=p(l),D=y(s),N=p(s)===s,_=await (null==f.isRTL?void 0:f.isRTL(g.floating)),I=M||(N||!j?[C(s)]:function(e){let t=C(e);return[w(e),t,w(t)]}(s)),F="none"!==k;!M&&F&&I.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?x:b;return t?b:x;case"left":case"right":return t?E:R;default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i}(s,j,k,_));let K=[s,...I],B=await T(t,L),U=[],W=(null==(r=u.flip)?void 0:r.overflows)||[];if(A&&U.push(B[O]),S){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(y(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=C(a)),[a,C(a)]}(l,c,_);U.push(B[e[0]],B[e[1]])}if(W=[...W,{placement:l,overflows:U}],!U.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=K[e];if(t&&("alignment"!==S||D===y(t)||W.every(e=>y(e.placement)!==D||e.overflows[0]>0)))return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(i=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(P){case"bestFit":{let e=null==(a=W.filter(e=>{if(F){let t=y(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:u,rects:c,platform:s,elements:f}=t,{apply:m=()=>{},...v}=d(e,t),g=await T(t,v),w=p(u),b=h(u),x="y"===y(u),{width:E,height:R}=c.floating;"top"===w||"bottom"===w?(o=w,l=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(l=w,o="end"===b?"top":"bottom");let C=R-g.top-g.bottom,A=E-g.left-g.right,S=i(R-g[o],C),M=i(E-g[l],A),P=!t.middlewareData.shift,k=S,j=M;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(j=A),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=C),P&&!b){let e=a(g.left,0),t=a(g.right,0),n=a(g.top,0),r=a(g.bottom,0);x?j=E-2*(0!==e||0!==t?e+t:a(g.left,g.right)):k=R-2*(0!==n||0!==r?n+r:a(g.top,g.bottom))}await m({...t,availableWidth:j,availableHeight:k});let L=await s.getDimensions(f.floating);return E!==L.width||R!==L.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{let e=k(await T(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:j(e)}}}case"escaped":{let e=k(await T(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:j(e)}}}default:return{}}}}}(e),options:[e,t]}),e_=(e,t)=>({...eT(e),options:[e,t]});var eI=n(3655),eF=n(5155),eK=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eF.jsx)(eI.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eF.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eK.displayName="Arrow";var eB=n(6101),eU=n(6081),eW=n(9033),eG=n(2712),eH="Popper",[ez,eV]=(0,eU.A)(eH),[eX,eq]=ez(eH),eY=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eF.jsx)(eX,{scope:t,anchor:o,onAnchorChange:i,children:n})};eY.displayName=eH;var eZ="PopperAnchor",e$=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eq(eZ,n),l=r.useRef(null),u=(0,eB.s)(t,l);return r.useEffect(()=>{a.onAnchorChange((null==o?void 0:o.current)||l.current)}),o?null:(0,eF.jsx)(eI.sG.div,{...i,ref:u})});e$.displayName=eZ;var eQ="PopperContent",[eJ,e0]=ez(eQ),e1=r.forwardRef((e,t)=>{var n,o,l,c,s,f,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:C=!1,updatePositionStrategy:A="optimized",onPlaced:S,...M}=e,P=eq(eQ,h),[T,k]=r.useState(null),j=(0,eB.s)(t,e=>k(e)),[L,O]=r.useState(null),D=function(e){let[t,n]=r.useState(void 0);return(0,eG.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(L),N=null!=(d=null==D?void 0:D.width)?d:0,_=null!=(p=null==D?void 0:D.height)?p:0,F="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},K=Array.isArray(x)?x:[x],B=K.length>0,U={padding:F,boundary:K.filter(e6),altBoundary:B},{refs:W,floatingStyles:G,placement:H,isPositioned:z,middlewareData:V}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);eA(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=a||m,E=l||g,R=r.useRef(null),C=r.useRef(null),A=r.useRef(f),S=null!=c,M=eP(c),P=eP(i),T=eP(s),k=r.useCallback(()=>{if(!R.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};P.current&&(e.platform=P.current),eE(R.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};j.current&&!eA(A.current,t)&&(A.current=t,eR.flushSync(()=>{d(t)}))})},[p,t,n,P,T]);eC(()=>{!1===s&&A.current.isPositioned&&(A.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let j=r.useRef(!1);eC(()=>(j.current=!0,()=>{j.current=!1}),[]),eC(()=>{if(x&&(R.current=x),E&&(C.current=E),x&&E){if(M.current)return M.current(x,E,k);k()}},[x,E,k,M,S]);let L=r.useMemo(()=>({reference:R,floating:C,setReference:w,setFloating:b}),[w,b]),O=r.useMemo(()=>({reference:x,floating:E}),[x,E]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=eM(O.floating,f.x),r=eM(O.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eS(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,O.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:k,refs:L,elements:O,floatingStyles:D}),[f,k,L,O,D])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=ea(e),h=l||c?[...p?er(p):[],...er(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let m=p&&f?function(e,t){let n,r=null,o=I(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),l();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=d;if(s||t(),!m||!v)return;let g=u(h),y=u(o.clientWidth-(p+m)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(h+v))+"px "+-u(p)+"px",threshold:a(0,i(1,f))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||eb(d,e.getBoundingClientRect())||c(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),l}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?es(e):null;return d&&function t(){let r=es(e);y&&!eb(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:P.anchor},middleware:[ek({mainAxis:v+_,alignmentAxis:y}),b&&ej({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?eL():void 0,...U}),b&&eO({...U}),eD({...U,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),L&&e_({element:L,padding:w}),e3({arrowWidth:N,arrowHeight:_}),C&&eN({strategy:"referenceHidden",...U})]}),[X,q]=e8(H),Y=(0,eW.c)(S);(0,eG.N)(()=>{z&&(null==Y||Y())},[z,Y]);let Z=null==(n=V.arrow)?void 0:n.x,$=null==(o=V.arrow)?void 0:o.y,Q=(null==(l=V.arrow)?void 0:l.centerOffset)!==0,[J,ee]=r.useState();return(0,eG.N)(()=>{T&&ee(window.getComputedStyle(T).zIndex)},[T]),(0,eF.jsx)("div",{ref:W.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:z?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:J,"--radix-popper-transform-origin":[null==(c=V.transformOrigin)?void 0:c.x,null==(s=V.transformOrigin)?void 0:s.y].join(" "),...(null==(f=V.hide)?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eF.jsx)(eJ,{scope:h,placedSide:X,onArrowChange:O,arrowX:Z,arrowY:$,shouldHideArrow:Q,children:(0,eF.jsx)(eI.sG.div,{"data-side":X,"data-align":q,...M,ref:j,style:{...M.style,animation:z?void 0:"none"}})})})});e1.displayName=eQ;var e2="PopperArrow",e5={top:"bottom",right:"left",bottom:"top",left:"right"},e9=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e0(e2,n),i=e5[o.placedSide];return(0,eF.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eF.jsx)(eK,{...r,ref:t,style:{...r.style,display:"block"}})})});function e6(e){return null!==e}e9.displayName=e2;var e3=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,f=s?0:e.arrowWidth,d=s?0:e.arrowHeight,[p,h]=e8(l),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+f/2,g=(null!=(a=null==(o=c.arrow)?void 0:o.y)?a:0)+d/2,y="",w="";return"bottom"===p?(y=s?m:"".concat(v,"px"),w="".concat(-d,"px")):"top"===p?(y=s?m:"".concat(v,"px"),w="".concat(u.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=s?m:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+d,"px"),w=s?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function e8(e){let[t,n="center"]=e.split("-");return[t,n]}var e4=eY,e7=e$,te=e1,tt=e9},8859:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function i(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},9196:(e,t,n)=>{n.d(t,{RG:()=>x,bL:()=>k,q7:()=>j});var r=n(2115),o=n(5185),i=n(7328),a=n(6101),l=n(6081),u=n(1285),c=n(3655),s=n(9033),f=n(5845),d=n(4315),p=n(5155),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,y,w]=(0,i.N)(v),[b,x]=(0,l.A)(v,[w]),[E,R]=b(v),C=r.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(A,{...e,ref:t})})}));C.displayName=v;var A=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:b,onEntryFocus:x,preventScrollOnEntryFocus:R=!1,...C}=e,A=r.useRef(null),S=(0,a.s)(t,A),M=(0,d.jH)(u),[P,k]=(0,f.i)({prop:g,defaultProp:null!=w?w:null,onChange:b,caller:v}),[j,L]=r.useState(!1),O=(0,s.c)(x),D=y(n),N=r.useRef(!1),[_,I]=r.useState(0);return r.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(h,O),()=>e.removeEventListener(h,O)},[O]),(0,p.jsx)(E,{scope:n,orientation:i,dir:M,loop:l,currentTabStopId:P,onItemFocus:r.useCallback(e=>k(e),[k]),onItemShiftTab:r.useCallback(()=>L(!0),[]),onFocusableItemAdd:r.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>I(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:j||0===_?-1:0,"data-orientation":i,...C,ref:S,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=D().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===P),...e].filter(Boolean).map(e=>e.ref.current),R)}}N.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>L(!1))})})}),S="RovingFocusGroupItem",M=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,children:s,...f}=e,d=(0,u.B)(),h=l||d,m=R(S,n),v=m.currentTabStopId===h,w=y(n),{onFocusableItemAdd:b,onFocusableItemRemove:x,currentTabStopId:E}=m;return r.useEffect(()=>{if(i)return b(),()=>x()},[i,b,x]),(0,p.jsx)(g.ItemSlot,{scope:n,id:h,focusable:i,active:a,children:(0,p.jsx)(c.sG.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...f,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?m.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return P[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=m.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>T(n))}}),children:"function"==typeof s?s({isCurrentTabStop:v,hasTabStop:null!=E}):s})})});M.displayName=S;var P={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var k=C,j=M},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return v},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return u},getLocationOrigin:function(){return a},getURL:function(){return l},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return s},stringifyError:function(){return w}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),i=0;i<r;i++)o[i]=arguments[i];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function l(){let{href:e}=window.location,t=a();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function s(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&c(n))return r;if(!r)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function w(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);