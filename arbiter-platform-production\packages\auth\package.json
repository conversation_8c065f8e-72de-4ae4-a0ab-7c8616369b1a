{"name": "@arbiter/auth", "version": "1.0.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.1", "nanoid": "^4.0.2", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-github2": "^0.1.12", "passport-jwt": "^4.0.1", "zod": "^3.21.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/passport": "^1.0.12", "@types/passport-google-oauth20": "^2.0.11", "@types/passport-github2": "^1.2.5", "@types/passport-jwt": "^3.0.8", "tsx": "^3.12.7", "typescript": "^5.1.6"}}