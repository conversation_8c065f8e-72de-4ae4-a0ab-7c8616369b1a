/**
 * Pay Per Crawl Admin Styles
 * Version: 3.0.0
 */

/* Variables */
:root {
    --ppc-primary: #2271b1;
    --ppc-primary-hover: #135e96;
    --ppc-success: #00a32a;
    --ppc-warning: #dba617;
    --ppc-danger: #d63638;
    --ppc-info: #2271b1;
    --ppc-dark: #1e1e1e;
    --ppc-light: #f0f0f1;
    --ppc-border: #c3c4c7;
    --ppc-shadow: 0 1px 3px rgba(0,0,0,0.12);
    --ppc-radius: 8px;
}

/* Dashboard Container */
.paypercrawl-dashboard {
    margin-right: 20px;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Header Section */
.ppc-header {
    background: linear-gradient(135deg, #2271b1 0%, #135e96 100%);
    color: white;
    padding: 40px;
    border-radius: var(--ppc-radius);
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
}

.ppc-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 400px;
    height: 400px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
}

.ppc-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.ppc-logo-section h1 {
    font-size: 32px;
    margin: 0;
    font-weight: 600;
    color: white;
}

.ppc-tagline {
    font-size: 16px;
    opacity: 0.9;
    margin-top: 5px;
}

.ppc-header-stats {
    display: flex;
    gap: 30px;
}

.ppc-mini-stat {
    text-align: center;
}

.ppc-mini-value {
    display: block;
    font-size: 28px;
    font-weight: 700;
}

.ppc-mini-label {
    display: block;
    font-size: 14px;
    opacity: 0.8;
    margin-top: 5px;
}

/* Status Indicator */
.ppc-status-indicator {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(0,0,0,0.2);
    padding: 8px 16px;
    border-radius: 20px;
}

.ppc-status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.ppc-status-indicator.online .ppc-status-dot {
    background: #00a32a;
}

.ppc-status-indicator.warning .ppc-status-dot {
    background: #dba617;
}

.ppc-status-indicator.offline .ppc-status-dot {
    background: #d63638;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.ppc-status-text {
    font-size: 13px;
    font-weight: 500;
}

/* Statistics Grid */
.ppc-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.ppc-stat-card {
    background: white;
    border: 1px solid var(--ppc-border);
    border-radius: var(--ppc-radius);
    padding: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ppc-stat-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.ppc-stat-card.primary {
    border-left: 4px solid var(--ppc-primary);
}

.ppc-stat-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.ppc-stat-icon {
    font-size: 24px;
}

.ppc-stat-header h3 {
    margin: 0;
    font-size: 16px;
    color: #50575e;
    font-weight: 500;
}

.ppc-stat-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--ppc-dark);
    margin-bottom: 10px;
}

.ppc-stat-change {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.ppc-stat-change.positive {
    color: var(--ppc-success);
}

.ppc-stat-change.negative {
    color: var(--ppc-danger);
}

.ppc-stat-meta {
    font-size: 14px;
    color: #787c82;
}

/* Two Column Layout */
.ppc-two-column {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

@media (max-width: 1200px) {
    .ppc-two-column {
        grid-template-columns: 1fr;
    }
}

/* Cards */
.ppc-card {
    background: white;
    border: 1px solid var(--ppc-border);
    border-radius: var(--ppc-radius);
    margin-bottom: 20px;
}

.ppc-card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--ppc-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ppc-card-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.ppc-card-actions {
    display: flex;
    gap: 10px;
}

/* Chart Container */
.ppc-chart-container {
    padding: 20px;
    height: 300px;
}

/* Revenue Breakdown */
.ppc-revenue-breakdown {
    padding: 20px;
}

.ppc-revenue-breakdown h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
}

.ppc-bot-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ppc-bot-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f1;
}

.ppc-bot-item:last-child {
    border-bottom: none;
}

.ppc-bot-name {
    display: flex;
    align-items: center;
    gap: 10px;
}

.ppc-bot-company {
    font-size: 12px;
    color: #787c82;
}

.ppc-bot-revenue {
    font-weight: 600;
    color: var(--ppc-success);
}

/* Activity Feed */
.ppc-activity-feed {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.ppc-activity-item {
    display: flex;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f1;
}

.ppc-activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f1;
    flex-shrink: 0;
}

.ppc-activity-content {
    flex: 1;
}

.ppc-activity-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.ppc-activity-meta {
    font-size: 13px;
    color: #787c82;
}

.ppc-activity-time {
    font-size: 12px;
    color: #a7aaad;
}

/* Setup Checklist */
.ppc-setup-checklist {
    padding: 20px;
}

.ppc-checklist-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
}

.ppc-checklist-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.ppc-checklist-icon.complete {
    background: var(--ppc-success);
    color: white;
}

.ppc-checklist-icon.incomplete {
    background: #f0f0f1;
    color: #787c82;
}

.ppc-checklist-text {
    flex: 1;
}

/* Optimization Tips */
.ppc-optimization-tips {
    padding: 20px;
}

.ppc-tip {
    display: flex;
    gap: 15px;
    padding: 15px;
    background: #f6f7f7;
    border-radius: 6px;
    margin-bottom: 15px;
}

.ppc-tip-icon {
    font-size: 24px;
}

.ppc-tip strong {
    display: block;
    margin-bottom: 5px;
}

.ppc-tip p {
    margin: 0 0 10px 0;
    color: #50575e;
    font-size: 14px;
}

/* Branding Section */
.ppc-branding {
    padding: 20px;
    text-align: center;
}

.ppc-branding ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.ppc-branding li {
    padding: 8px 0;
    font-size: 14px;
}

/* Buttons */
.button.ppc-button {
    background: var(--ppc-primary);
    border-color: var(--ppc-primary);
    color: white;
}

.button.ppc-button:hover {
    background: var(--ppc-primary-hover);
    border-color: var(--ppc-primary-hover);
}

/* Forms */
.ppc-form-table {
    width: 100%;
}

.ppc-form-table th {
    text-align: left;
    padding: 20px 10px 20px 0;
    width: 200px;
    vertical-align: top;
}

.ppc-form-table td {
    padding: 15px 0;
}

.ppc-form-table input[type="text"],
.ppc-form-table input[type="number"],
.ppc-form-table input[type="email"],
.ppc-form-table select,
.ppc-form-table textarea {
    width: 100%;
    max-width: 400px;
}

.ppc-form-table .description {
    font-size: 13px;
    color: #646970;
    margin-top: 5px;
}

/* Alerts */
.ppc-alert {
    padding: 12px 20px;
    border-radius: 4px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.ppc-alert.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.ppc-alert.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.ppc-alert.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.ppc-alert.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Loading Spinner */
.ppc-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0,0,0,0.1);
    border-radius: 50%;
    border-top-color: var(--ppc-primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .ppc-header {
        padding: 20px;
    }
    
    .ppc-header-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .ppc-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .ppc-form-table th {
        display: block;
        width: 100%;
        padding-bottom: 5px;
    }
    
    .ppc-form-table td {
        display: block;
        width: 100%;
    }
}
