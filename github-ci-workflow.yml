name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        php-version: [7.4, 8.0, 8.1, 8.2]
        wordpress-version: [5.0, 5.5, 6.0, latest]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-version }}
        extensions: mbstring, intl, pdo_mysql
        coverage: xdebug
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install PHP dependencies
      run: composer install --no-progress --prefer-dist --optimize-autoloader
    
    - name: Install Node dependencies
      run: npm ci
    
    - name: Run PHP CodeSniffer
      run: composer run-script phpcs
    
    - name: Run PHPUnit tests
      run: composer run-script test
    
    - name: Run JavaScript tests
      run: npm test
    
    - name: Build assets
      run: npm run build
    
    - name: WordPress Plugin Check
      run: |
        wget https://github.com/WordPress/plugin-check/releases/latest/download/plugin-check.phar
        php plugin-check.phar --format=json .

  security:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run npm security audit
      run: npm audit --audit-level moderate
    
    - name: Run composer security audit
      run: composer audit

  lint:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run ESLint
      run: npm run lint
    
    - name: Run Prettier check
      run: npm run format:check
