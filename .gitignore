# WordPress
wp-config.php
wp-content/uploads/
wp-content/cache/
wp-content/backup-db/
wp-content/advanced-cache.php
wp-content/wp-cache-config.php
wp-content/cache/
wp-content/backups/
sitemap.xml
*.log

# Plugin specific
.env
.env.local
.env.production
config.local.php

# Dependencies
node_modules/
vendor/
composer.lock
package-lock.json

# Build files
dist/
build/
*.min.js
*.min.css

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db
*.tmp

# Testing
coverage/
.nyc_output/
phpunit.xml.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database
*.sql.gz
*.sql.bak

# Cloudflare
.wrangler/
wrangler.toml.local

# Stripe
stripe-cli.log
