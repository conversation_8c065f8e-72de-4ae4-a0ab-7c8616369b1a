"use strict";(()=>{var e={};e.id=573,e.ids=[573],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1186:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{POST:()=>u});var o=t(6559),n=t(8088),a=t(7719),i=t(2190),p=t(9991);async function u(e){try{let r=e.headers.get("authorization");if(r?.replace("Bearer ","")!==process.env.ADMIN_API_KEY)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{email:t,name:s,token:o}=await e.json();if(!t||!s||!o)return i.NextResponse.json({error:"Missing required fields: email, name, token"},{status:400});console.log("Testing email send with:",{email:t,name:s,token:o}),console.log("RESEND_API_KEY exists:",!!process.env.RESEND_API_KEY),console.log("NEXT_PUBLIC_APP_URL:","https://paypercrawl.tech");try{let e=await (0,p.YL)(t,s,o);return console.log("Email sent successfully:",e),i.NextResponse.json({success:!0,message:"Test email sent successfully",result:e})}catch(e){return console.error("Email sending failed:",e),i.NextResponse.json({success:!1,error:"Email sending failed",details:e instanceof Error?e.message:String(e)},{status:500})}}catch(e){return console.error("Test email endpoint error:",e),i.NextResponse.json({error:"Internal server error",details:e instanceof Error?e.message:String(e)},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/test-email/route",pathname:"/api/test-email",filename:"route",bundlePath:"app/api/test-email/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\test-email\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:x}=l;function m(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},2502:e=>{e.exports=import("prettier/plugins/html")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3505:e=>{e.exports=import("prettier/standalone")},4297:e=>{e.exports=require("async_hooks")},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{e.exports=require("crypto")},6330:e=>{e.exports=require("@prisma/client")},7075:e=>{e.exports=require("node:stream")},7910:e=>{e.exports=require("stream")},8354:e=>{e.exports=require("util")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,123],()=>t(1186));module.exports=s})();