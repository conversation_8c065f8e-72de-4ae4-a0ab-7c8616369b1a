=== Pay Per Crawl ===
Contributors: paypercrawl
Tags: ai, bot-detection, monetization, crawling, revenue
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 3.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Advanced AI Bot Detection & Monetization Platform - Turn Every AI Crawl Into Revenue

== Description ==

Pay Per Crawl is a revolutionary WordPress plugin that detects AI bots crawling your website and helps you monetize that traffic. Every time an AI bot like <PERSON><PERSON><PERSON><PERSON>, <PERSON>, or GPTBot accesses your content, you earn revenue.

**Key Features:**

* **Real-time Bot Detection** - Identifies major AI bots including ChatGPT, Claude, GPTBot, and more
* **Revenue Tracking** - Track earnings from each bot detection
* **Comprehensive Dashboard** - View statistics, recent detections, and total revenue
* **Customizable Settings** - Configure detection sensitivity and revenue rates
* **Detailed Logging** - Complete audit trail of all bot interactions

**Supported AI Bots:**
* ChatGPT (OpenAI)
* <PERSON> (Anthropic)
* GPTBot
* Google-Extended
* CCBot
* Bard
* Bing Bot

== Installation ==

1. Upload the plugin files to `/wp-content/plugins/pay-per-crawl/`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Go to Pay Per Crawl in your admin menu to configure settings
4. Start earning revenue from AI bot traffic!

== Frequently Asked Questions ==

= How does bot detection work? =

The plugin analyzes user agent strings and other request headers to identify known AI bot signatures.

= How much can I earn? =

Revenue depends on your traffic and bot activity. The default rate is $0.05 per detection, but this is configurable.

= Is this legal? =

Yes, detecting and monetizing bot traffic on your own website is completely legal.

== Screenshots ==

1. Dashboard showing bot detection statistics
2. Settings page for configuration
3. Recent detections table

== Changelog ==

= 3.0.0 =
* Initial release
* Real-time bot detection
* Revenue tracking dashboard
* Comprehensive settings panel

== Upgrade Notice ==

= 3.0.0 =
Initial release of Pay Per Crawl plugin.
