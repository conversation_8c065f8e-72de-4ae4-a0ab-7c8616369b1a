<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arbiter Platform - Creator & AI Company Prototype Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .demo-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px 20px;
            gap: 30px;
            flex-wrap: wrap;
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            min-width: 350px;
            max-width: 400px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .demo-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }

        .demo-card h2 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            color: #333;
        }

        .demo-card p {
            font-size: 1rem;
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .stat-item {
            background: rgba(103, 126, 234, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }

        .demo-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(103, 126, 234, 0.3);
        }

        .features-list {
            text-align: left;
            margin: 20px 0;
        }

        .features-list li {
            margin: 8px 0;
            color: #555;
            list-style: none;
            position: relative;
            padding-left: 25px;
        }

        .features-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }

        .footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            color: white;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .tech-stack {
            margin-top: 20px;
            padding: 20px;
            background: rgba(103, 126, 234, 0.1);
            border-radius: 10px;
        }

        .tech-stack h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .tech-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .tech-badge {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .demo-container {
                flex-direction: column;
                padding: 20px;
            }
            
            .demo-card {
                min-width: unset;
                max-width: 100%;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Arbiter Platform</h1>
        <p>Next-Generation Content Creator & AI Company Marketplace</p>
    </div>

    <div class="demo-container">
        <!-- Creator Dashboard Demo -->
        <div class="demo-card">
            <span class="demo-icon">🎨</span>
            <h2>Creator Dashboard</h2>
            <p>Powerful tools for content creators to monetize their work, track revenue, and manage licensing agreements.</p>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">$12,543</div>
                    <div class="stat-label">Total Revenue</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">156</div>
                    <div class="stat-label">Content Items</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">1,247</div>
                    <div class="stat-label">Licenses Sold</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">89%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>

            <ul class="features-list">
                <li>Revenue Analytics & Forecasting</li>
                <li>Content Library Management</li>
                <li>Automated License Generation</li>
                <li>Real-time Performance Tracking</li>
                <li>Bulk Upload & Organization</li>
            </ul>

            <button class="demo-btn" onclick="showCreatorDemo()">View Creator Portal</button>
        </div>

        <!-- AI Company Dashboard Demo -->
        <div class="demo-card">
            <span class="demo-icon">🤖</span>
            <h2>AI Company Dashboard</h2>
            <p>Comprehensive platform for AI companies to discover, license, and manage content for training and development.</p>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">$48,750</div>
                    <div class="stat-label">Total Spent</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">342</div>
                    <div class="stat-label">Licenses Owned</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">156K</div>
                    <div class="stat-label">API Calls</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">12</div>
                    <div class="stat-label">Active Projects</div>
                </div>
            </div>

            <ul class="features-list">
                <li>AI-Powered Content Discovery</li>
                <li>Bulk License Management</li>
                <li>Usage Analytics & Reporting</li>
                <li>API Integration & Monitoring</li>
                <li>Compliance & Legal Tools</li>
            </ul>

            <button class="demo-btn" onclick="showAIDemo()">View AI Portal</button>
        </div>
    </div>

    <div class="footer">
        <div class="tech-stack">
            <h3>🛠️ Technology Stack</h3>
            <div class="tech-badges">
                <span class="tech-badge">React 18</span>
                <span class="tech-badge">TypeScript</span>
                <span class="tech-badge">Node.js</span>
                <span class="tech-badge">PostgreSQL</span>
                <span class="tech-badge">Redis</span>
                <span class="tech-badge">Docker</span>
                <span class="tech-badge">Elasticsearch</span>
                <span class="tech-badge">MinIO</span>
                <span class="tech-badge">Stripe</span>
                <span class="tech-badge">WebSockets</span>
            </div>
        </div>
        <p style="margin-top: 20px;">
            Enhanced prototype ready for market testing • WSL development environment • 8 microservices architecture
        </p>
    </div>

    <script>
        function showCreatorDemo() {
            alert(`🎨 Creator Dashboard Features:

✅ Revenue Tracking: $12,543 total earned
✅ Content Management: 156 items uploaded
✅ License Sales: 1,247 licenses sold
✅ Performance Analytics: 89% success rate

Key Features:
• Real-time revenue dashboard
• Drag & drop content upload
• Automated license generation
• Advanced analytics & forecasting
• Bulk content organization
• Payment integration with Stripe

Ready for immediate deployment in WSL environment!`);
        }

        function showAIDemo() {
            alert(`🤖 AI Company Dashboard Features:

✅ Total Investment: $48,750 spent
✅ License Portfolio: 342 licenses owned
✅ API Usage: 156,432 calls this month
✅ Active Projects: 12 concurrent projects

Key Features:
• AI-powered content discovery
• Bulk license acquisition
• Usage monitoring & analytics
• API integration & rate limiting
• Compliance tracking
• Advanced search & filtering

Ready for immediate deployment in WSL environment!`);
        }

        // Auto-scroll animation
        window.addEventListener('load', function() {
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
