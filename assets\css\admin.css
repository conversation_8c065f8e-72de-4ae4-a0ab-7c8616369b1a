/**
 * PayPerCrawl Pro - Enterprise Dashboard Styles
 * 
 * @package PayPerCrawl
 * @version 4.0.0
 */

/* Reset and Base Styles */
.paypercrawl-dashboard * {
    box-sizing: border-box;
}

.paypercrawl-dashboard {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Robot<PERSON>, sans-serif;
    background: #f6f7fb;
    margin: 0 -20px 0 -22px;
    min-height: 100vh;
}

/* Header */
.ppc-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 2rem 3rem;
    margin-bottom: -2rem;
    position: relative;
    overflow: hidden;
}

.ppc-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="60" cy="40" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="40" cy="70" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.ppc-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.ppc-logo h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ppc-icon {
    font-size: 2.8rem;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.ppc-version {
    background: rgba(255,255,255,0.2);
    padding: 0.3rem 0.8rem;
    border-radius: 2rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.ppc-tagline {
    margin: 0.5rem 0 0;
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

.ppc-header-actions {
    display: flex;
    gap: 1rem;
}

.ppc-refresh-btn, .ppc-export-btn {
    background: rgba(255,255,255,0.15);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.ppc-refresh-btn:hover, .ppc-export-btn:hover {
    background: rgba(255,255,255,0.25);
    transform: translateY(-1px);
}

/* Alerts */
.ppc-alerts {
    max-width: 1400px;
    margin: 0 auto 2rem;
    padding: 0 2rem;
}

.ppc-alert {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: relative;
}

.ppc-alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-left: 4px solid #28a745;
    color: #155724;
}

.ppc-alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left: 4px solid #ffc107;
    color: #856404;
}

.ppc-alert-error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.ppc-alert-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
}

.ppc-alert-content h4 {
    margin: 0 0 0.5rem;
    font-weight: 600;
}

.ppc-alert-content p {
    margin: 0;
    opacity: 0.9;
}

.ppc-alert-dismiss {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    opacity: 0.5;
    transition: opacity 0.2s;
}

.ppc-alert-dismiss:hover {
    opacity: 1;
}

/* Metrics Grid */
.ppc-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    max-width: 1400px;
    margin: 0 auto 3rem;
    padding: 0 2rem;
}

.ppc-metric-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.ppc-metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.ppc-metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.ppc-metric-card h3 {
    margin: 0 0 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ppc-metric-value {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.ppc-metric-change {
    display: flex;
    align-items: center;
    font-size: 0.85rem;
    font-weight: 500;
}

.ppc-metric-change.positive {
    color: #28a745;
}

.ppc-metric-change.negative {
    color: #dc3545;
}

.ppc-metric-change .dashicons {
    margin-right: 0.3rem;
}

.ppc-metric-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    font-size: 2rem;
    opacity: 0.1;
}

/* Revenue Card Specific */
.ppc-revenue-card .ppc-metric-value {
    color: #28a745;
}

.ppc-revenue-card::before {
    background: linear-gradient(90deg, #28a745, #20c997);
}

/* Dashboard Main Layout */
.ppc-dashboard-main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.ppc-dashboard-left, .ppc-dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Widgets */
.ppc-dashboard-widget {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.ppc-widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.ppc-widget-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.ppc-widget-content {
    padding: 2rem;
}

/* Charts */
.ppc-chart-container {
    position: relative;
    margin-bottom: 1.5rem;
}

.ppc-revenue-insights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
}

.ppc-insight {
    text-align: center;
    font-size: 0.9rem;
}

.ppc-bot-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.ppc-chart-half h4 {
    margin: 0 0 1rem;
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
}

/* Data Table */
.ppc-data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.ppc-data-table th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #2d3748;
    border-bottom: 1px solid #e9ecef;
}

.ppc-data-table td {
    padding: 1rem;
    border-bottom: 1px solid #f1f3f4;
}

.ppc-data-table tr:hover {
    background: #f8f9fa;
}

.ppc-bot-name {
    font-weight: 600;
    color: #2d3748;
}

.ppc-bot-type {
    display: inline-block;
    background: #e9ecef;
    color: #6c757d;
    padding: 0.2rem 0.5rem;
    border-radius: 0.3rem;
    font-size: 0.75rem;
    margin-left: 0.5rem;
}

/* Activity Feed */
.ppc-activity-feed {
    max-height: 400px;
    overflow-y: auto;
}

.ppc-activity-item {
    display: flex;
    align-items: flex-start;
    padding: 1rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.ppc-activity-item:last-child {
    border-bottom: none;
}

.ppc-activity-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    margin-top: 0.2rem;
}

.ppc-activity-content {
    flex: 1;
}

.ppc-activity-title {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 0.3rem;
}

.ppc-activity-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #6c757d;
}

.ppc-activity-revenue {
    font-weight: 600;
    color: #28a745;
}

.ppc-live-indicator {
    animation: pulse 2s infinite;
    font-size: 0.8rem;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Action Buttons */
.ppc-action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.ppc-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #2d3748;
}

.ppc-action-btn:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    color: #2d3748;
}

.ppc-action-btn .dashicons {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* System Health */
.ppc-health-indicators {
    margin-bottom: 1.5rem;
}

.ppc-health-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.ppc-health-item:last-child {
    border-bottom: none;
}

.ppc-health-status {
    margin-right: 1rem;
}

.ppc-status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
}

.ppc-status-good .ppc-status-dot {
    background: #28a745;
}

.ppc-status-warning .ppc-status-dot {
    background: #ffc107;
}

.ppc-status-critical .ppc-status-dot {
    background: #dc3545;
}

.ppc-health-info {
    flex: 1;
}

.ppc-health-name {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 0.2rem;
}

.ppc-health-value {
    font-size: 0.9rem;
    color: #6c757d;
}

.ppc-health-summary {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
}

.ppc-score-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-left: 0.5rem;
}

.ppc-score-good {
    color: #28a745;
}

.ppc-score-warning {
    color: #ffc107;
}

.ppc-score-critical {
    color: #dc3545;
}

/* Footer */
.ppc-dashboard-footer {
    margin-top: 3rem;
    padding: 2rem;
    background: white;
    border-top: 1px solid #e9ecef;
}

.ppc-footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    font-size: 0.9rem;
    color: #6c757d;
}

.ppc-last-update {
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .ppc-dashboard-main {
        grid-template-columns: 1fr;
    }
    
    .ppc-bot-charts {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .paypercrawl-dashboard {
        margin: 0 -10px;
    }
    
    .ppc-header {
        padding: 1.5rem 1rem 2rem;
    }
    
    .ppc-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .ppc-logo h1 {
        font-size: 2rem;
    }
    
    .ppc-metrics-grid,
    .ppc-dashboard-main {
        padding: 0 1rem;
    }
    
    .ppc-metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .ppc-action-buttons {
        grid-template-columns: 1fr;
    }
    
    .ppc-widget-content {
        padding: 1rem;
    }
}

/* Loading States */
.ppc-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #6c757d;
}

.ppc-spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Indicators */
.ppc-status-active {
    color: #28a745;
}

.ppc-status-inactive {
    color: #dc3545;
}

.ppc-status-warning {
    color: #ffc107;
}

/* Utility Classes */
.ppc-text-center {
    text-align: center;
}

.ppc-text-right {
    text-align: right;
}

.ppc-mb-0 {
    margin-bottom: 0;
}

.ppc-mt-1 {
    margin-top: 1rem;
}

.ppc-hidden {
    display: none;
}

/* End of CSS */
