"use strict";(()=>{var e={};e.id=280,e.ids=[280],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2502:e=>{e.exports=import("prettier/plugins/html")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3505:e=>{e.exports=import("prettier/standalone")},4220:(e,r,t)=>{t.d(r,{g:()=>u});var n=t(5511);let a={randomUUID:n.randomUUID},i=new Uint8Array(256),s=i.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let l=function(e,r,t){if(a.randomUUID&&!r&&!e)return a.randomUUID();let l=(e=e||{}).random??e.rng?.()??(s>i.length-16&&((0,n.randomFillSync)(i),s=0),i.slice(s,s+=16));if(l.length<16)throw Error("Random bytes length must be >= 16");if(l[6]=15&l[6]|64,l[8]=63&l[8]|128,r){if((t=t||0)<0||t+16>r.length)throw RangeError(`UUID byte range ${t}:${t+15} is out of buffer bounds`);for(let e=0;e<16;++e)r[t+e]=l[e];return r}return function(e,r=0){return(o[e[r+0]]+o[e[r+1]]+o[e[r+2]]+o[e[r+3]]+"-"+o[e[r+4]]+o[e[r+5]]+"-"+o[e[r+6]]+o[e[r+7]]+"-"+o[e[r+8]]+o[e[r+9]]+"-"+o[e[r+10]]+o[e[r+11]]+o[e[r+12]]+o[e[r+13]]+o[e[r+14]]+o[e[r+15]]).toLowerCase()}(l)};function u(){return`invite_${l().replace(/-/g,"")}`}},4297:e=>{e.exports=require("async_hooks")},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{e.exports=require("crypto")},6192:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>y,serverHooks:()=>j,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>g});var n={};t.r(n),t.d(n,{GET:()=>f,POST:()=>w});var a=t(6559),i=t(8088),s=t(7719),o=t(2190),l=t(5069),u=t(639),p=t(4250),d=t(4220),c=t(9991);let m=u.Ik({name:u.Yj().min(2,"Name must be at least 2 characters"),email:u.Yj().email("Invalid email address"),website:u.Yj().url().optional().or(u.eu("")),companySize:u.k5(["small","medium","large"]).optional(),useCase:u.Yj().optional()});async function w(e){try{let r=await e.json(),t=m.parse(r);if(await l.db.waitlistEntry.findUnique({where:{email:t.email}}))return o.NextResponse.json({error:"This email is already on the waitlist"},{status:400});await l.db.waitlistEntry.create({data:{name:t.name,email:t.email,website:t.website||null,companySize:t.companySize||null,useCase:t.useCase||null,inviteToken:(0,d.g)()}});let n=await x(t.email);try{await (0,c.F7)(t.email,t.name,n)}catch(e){console.error("Failed to send confirmation email:",e)}return o.NextResponse.json({message:"Successfully joined waitlist",position:n})}catch(e){if(console.error("Waitlist join error:",e),e instanceof p.G)return o.NextResponse.json({error:"Validation failed",details:e.issues},{status:400});return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e){let r=await l.db.waitlistEntry.findUnique({where:{email:e}});return r?await l.db.waitlistEntry.count({where:{createdAt:{lte:r.createdAt}}}):0}async function f(){try{let e=await l.db.waitlistEntry.findMany({orderBy:{createdAt:"desc"}});return o.NextResponse.json(e)}catch(e){return console.error("Error fetching waitlist:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let y=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/waitlist/join/route",pathname:"/api/waitlist/join",filename:"route",bundlePath:"app/api/waitlist/join/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\waitlist\\join\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:h,workUnitAsyncStorage:g,serverHooks:j}=y;function v(){return(0,s.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:g})}},6330:e=>{e.exports=require("@prisma/client")},7075:e=>{e.exports=require("node:stream")},7910:e=>{e.exports=require("stream")},8354:e=>{e.exports=require("util")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[447,580,639,123],()=>t(6192));module.exports=n})();