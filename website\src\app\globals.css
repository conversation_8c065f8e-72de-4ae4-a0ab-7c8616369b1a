@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --sidebar: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 217.2 91.2% 59.8%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 6.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 19.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 19.5%;
  --muted-foreground: 215 20.2% 70.1%;
  --accent: 217.2 32.6% 19.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 45.6%;
  --border: 217.2 32.6% 19.5%;
  --input: 217.2 32.6% 19.5%;
  --ring: 212.7 26.8% 83.9%;
  --chart-1: 220 70% 55%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 70% 55%;
  --chart-5: 340 70% 55%;
  --sidebar: 240 5.9% 10%;
  --sidebar-foreground: 220 13% 91%;
  --sidebar-primary: 224.3 76.3% 94.1%;
  --sidebar-primary-foreground: 220.9 39.3% 11%;
  --sidebar-accent: 240 3.7% 15.9%;
  --sidebar-accent-foreground: 220 13% 91%;
  --sidebar-border: 240 3.7% 15.9%;
  --sidebar-ring: 217.2 91.2% 59.8%;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Enhanced dark mode support */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply text-foreground;
  }

  p {
    @apply text-foreground;
  }

  /* Fix for hard-coded colors in dark mode */
  .dark .text-slate-900 {
    @apply text-foreground;
  }

  .dark .text-slate-700 {
    @apply text-foreground;
  }

  .dark .text-slate-600 {
    @apply text-muted-foreground;
  }

  .dark .bg-white {
    @apply bg-card;
  }

  .dark .bg-slate-50 {
    @apply bg-muted/30;
  }

  .dark .shadow-lg {
    @apply shadow-xl shadow-black/20;
  }

  /* Improve button contrast in dark mode */
  .dark .outline {
    @apply border-border;
  }

  /* Improve input contrast in dark mode */
  .dark input {
    @apply bg-background border-border text-foreground;
  }

  /* Improve card contrast in dark mode */
  .dark .card {
    @apply bg-card border-border;
  }

  /* Improve gradient backgrounds for dark mode */
  .dark .bg-gradient-to-br {
    background: linear-gradient(
      to bottom right,
      hsl(var(--background)),
      hsl(var(--muted))
    );
  }
}
