# CrawlGuard WP Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Your Neon PostgreSQL connection string
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Your custom API domain (update with your actual domain)
API_BASE_URL=https://api.creativeinteriorsstudio.com/v1
API_VERSION=1.0.0
ENVIRONMENT=production

# =============================================================================
# CLOUDFLARE CONFIGURATION
# =============================================================================
# Your Cloudflare account ID (found in Cloudflare dashboard)
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id

# Your Cloudflare API token (create in Cloudflare dashboard)
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token

# Your Cloudflare zone ID for your domain
CLOUDFLARE_ZONE_ID=your_cloudflare_zone_id

# =============================================================================
# STRIPE CONFIGURATION (Required for payments)
# =============================================================================
# Stripe secret key (starts with sk_live_ for production or sk_test_ for testing)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key

# Stripe publishable key (starts with pk_live_ for production or pk_test_ for testing)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Stripe webhook secret (for webhook signature verification)
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Stripe Connect client ID (for marketplace functionality)
STRIPE_CONNECT_CLIENT_ID=ca_your_connect_client_id

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT secret for token signing (generate a random 32+ character string)
JWT_SECRET=your_super_secure_jwt_secret_key_here_32_chars_minimum

# API key encryption salt (generate a random string)
API_KEY_SALT=your_random_salt_for_api_key_encryption

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
# Enable rate limiting (true/false)
RATE_LIMIT_ENABLED=true

# Rate limits per hour by tier
RATE_LIMIT_FREE=100
RATE_LIMIT_PRO=1000
RATE_LIMIT_BUSINESS=5000

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
# Enable debug logging (true/false)
DEBUG_ENABLED=false

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# External monitoring service API key (optional)
MONITORING_API_KEY=your_monitoring_service_api_key

# =============================================================================
# WORDPRESS CONFIGURATION
# =============================================================================
# WordPress site URL (your main website)
WORDPRESS_SITE_URL=https://creativeinteriorsstudio.com

# WordPress admin email
WORDPRESS_ADMIN_EMAIL=<EMAIL>

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Development mode (true/false)
DEV_MODE=false

# Local development API URL
DEV_API_URL=http://localhost:8787

# Test database URL (for development/testing)
TEST_DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/crawlguard_test

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Backup storage configuration (optional)
BACKUP_STORAGE_PROVIDER=s3
BACKUP_STORAGE_BUCKET=crawlguard-backups
BACKUP_STORAGE_REGION=us-east-1
BACKUP_ACCESS_KEY=your_backup_access_key
BACKUP_SECRET_KEY=your_backup_secret_key

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
# Email service configuration (optional)
EMAIL_SERVICE_PROVIDER=sendgrid
EMAIL_API_KEY=your_email_service_api_key
EMAIL_FROM_ADDRESS=<EMAIL>

# Slack webhook for alerts (optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================
# Google Analytics tracking ID (optional)
GA_TRACKING_ID=GA-XXXXXXXXX-X

# Mixpanel project token (optional)
MIXPANEL_TOKEN=your_mixpanel_token

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable specific features
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_MULTI_SITE=true
FEATURE_CUSTOM_PRICING=true
FEATURE_WEBHOOK_SUPPORT=true
