(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var s=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:n,asChild:l=!1,...o}=e,c=l?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:a,size:n,className:t})),...o})}},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},3394:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>K});var s=a(5155),r=a(2115),n=a(6695),i=a(285),d=a(2523),l=a(5057),o=a(704),c=a(9434);function u(e){let{className:t,...a}=e;return(0,s.jsx)(o.bL,{"data-slot":"tabs",className:(0,c.cn)("flex flex-col gap-2",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(o.B8,{"data-slot":"tabs-list",className:(0,c.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(o.l9,{"data-slot":"tabs-trigger",className:(0,c.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(o.UC,{"data-slot":"tabs-content",className:(0,c.cn)("flex-1 outline-none",t),...a})}var p=a(6126);function v(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,c.cn)("w-full caption-bottom text-sm",t),...a})})}function f(e){let{className:t,...a}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,c.cn)("[&_tr]:border-b",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,c.cn)("[&_tr:last-child]:border-0",t),...a})}function b(e){let{className:t,...a}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,c.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function j(e){let{className:t,...a}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,c.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function w(e){let{className:t,...a}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,c.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}var y=a(3081),N=a(6474),k=a(5196),A=a(7863);function z(e){let{...t}=e;return(0,s.jsx)(y.bL,{"data-slot":"select",...t})}function _(e){let{...t}=e;return(0,s.jsx)(y.WT,{"data-slot":"select-value",...t})}function C(e){let{className:t,size:a="default",children:r,...n}=e;return(0,s.jsxs)(y.l9,{"data-slot":"select-trigger","data-size":a,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[r,(0,s.jsx)(y.In,{asChild:!0,children:(0,s.jsx)(N.A,{className:"size-4 opacity-50"})})]})}function S(e){let{className:t,children:a,position:r="popper",...n}=e;return(0,s.jsx)(y.ZL,{children:(0,s.jsxs)(y.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...n,children:[(0,s.jsx)(R,{}),(0,s.jsx)(y.LM,{className:(0,c.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(B,{})]})})}function E(e){let{className:t,children:a,...r}=e;return(0,s.jsxs)(y.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(y.VF,{children:(0,s.jsx)(k.A,{className:"size-4"})})}),(0,s.jsx)(y.p4,{children:a})]})}function R(e){let{className:t,...a}=e;return(0,s.jsx)(y.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(A.A,{className:"size-4"})})}function B(e){let{className:t,...a}=e;return(0,s.jsx)(y.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(N.A,{className:"size-4"})})}var T=a(8539),P=a(5525),Z=a(8749),L=a(2657),F=a(7434),W=a(7580),$=a(1154),I=a(2486),D=a(6671),J=a(6752),O=a(6874),U=a.n(O);function K(){let[e,t]=(0,r.useState)(""),[a,o]=(0,r.useState)(!1),[y,N]=(0,r.useState)(!1),[k,A]=(0,r.useState)([]),[R,B]=(0,r.useState)([]),[O,K]=(0,r.useState)(!1),[V,Y]=(0,r.useState)(null),[q,M]=(0,r.useState)(""),[X,Q]=(0,r.useState)("");(0,r.useEffect)(()=>{let e=localStorage.getItem("adminKey");e&&t(e)},[]);let G=()=>{"paypercrawl_admin_2025_secure_key"===e?(localStorage.setItem("adminKey",e),o(!0),D.oR.success("Authentication successful"),H()):D.oR.error("Invalid admin key")},H=async()=>{K(!0);try{let[t,a]=await Promise.all([fetch("/api/admin/applications",{headers:{Authorization:"Bearer ".concat(e)}}),fetch("/api/admin/waitlist",{headers:{Authorization:"Bearer ".concat(e)}})]);if(t.ok){let e=await t.json();A(e.applications)}if(a.ok){let e=await a.json();B(e.waitlistEntries)}}catch(e){console.error("Error loading data:",e),D.oR.error("Failed to load data")}finally{K(!1)}},ee=async()=>{if(V)try{(await fetch("/api/admin/applications",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify({id:V.id,status:X,notes:q})})).ok?(D.oR.success("Application updated successfully"),H(),Y(null)):D.oR.error("Failed to update application")}catch(e){console.error("Error updating application:",e),D.oR.error("An error occurred while updating the application")}},et=async t=>{try{let a=await fetch("/api/waitlist/invite",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,adminKey:e})});if(a.ok)D.oR.success("Invite sent successfully"),H();else{let e=await a.json();D.oR.error("Failed to send invite: ".concat(e.error))}}catch(e){console.error("Error sending invite:",e),D.oR.error("An error occurred while sending the invite")}};return((0,r.useEffect)(()=>{V&&(M(V.notes||""),Q(V.status))},[V]),a)?(0,s.jsxs)("div",{className:"min-h-screen bg-background text-foreground",children:[(0,s.jsx)("header",{className:"sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,s.jsxs)("div",{className:"container flex h-14 items-center",children:[(0,s.jsxs)(U(),{href:"/",className:"mr-6 flex items-center space-x-2",children:[(0,s.jsx)(P.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{className:"font-bold",children:"PayperCrawl Admin"})]}),(0,s.jsxs)("div",{className:"flex flex-1 items-center justify-end space-x-4",children:[(0,s.jsx)(J.c,{}),(0,s.jsx)(i.$,{variant:"outline",onClick:()=>{localStorage.removeItem("adminKey"),o(!1),D.oR.info("Logged out")},children:"Logout"})]})]})}),(0,s.jsx)("main",{className:"container mx-auto p-4 sm:p-6 lg:p-8",children:(0,s.jsxs)(u,{defaultValue:"applications",children:[(0,s.jsxs)(x,{className:"grid w-full grid-cols-2",children:[(0,s.jsxs)(h,{value:"applications",children:[(0,s.jsx)(F.A,{className:"mr-2 h-4 w-4"}),"Applications (",k.length,")"]}),(0,s.jsxs)(h,{value:"waitlist",children:[(0,s.jsx)(W.A,{className:"mr-2 h-4 w-4"}),"Waitlist (",R.length,")"]})]}),(0,s.jsx)(m,{value:"applications",children:(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"Beta Applications"}),(0,s.jsx)(n.BT,{children:"Review and manage applications for the beta program."})]}),(0,s.jsx)(n.Wu,{children:O?(0,s.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,s.jsx)($.A,{className:"h-8 w-8 animate-spin"})}):(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"md:col-span-1",children:[(0,s.jsx)("h3",{className:"font-bold mb-4",children:"Applicants"}),(0,s.jsx)("div",{className:"space-y-2 max-h-[600px] overflow-y-auto",children:k.map(e=>(0,s.jsx)(i.$,{variant:(null==V?void 0:V.id)===e.id?"secondary":"ghost",className:"w-full justify-start h-auto py-2",onClick:()=>Y(e),children:(0,s.jsxs)("div",{className:"text-left",children:[(0,s.jsx)("p",{className:"font-semibold",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email}),(0,s.jsx)(p.E,{variant:"outline",className:"mt-1",children:e.status})]})},e.id))})]}),(0,s.jsx)("div",{className:"md:col-span-2",children:V?(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:V.name}),(0,s.jsxs)(n.BT,{children:["Applied on"," ",(0,c.Y)(new Date(V.createdAt))]})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Email:"})," ",V.email]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Phone:"})," ",V.phone||"N/A"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Website:"})," ",(0,s.jsx)("a",{href:V.website,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:V.website})]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Position:"})," ",V.position]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:"Cover Letter:"})}),(0,s.jsx)("p",{className:"text-muted-foreground p-2 border rounded-md bg-muted/50",children:V.coverLetter||"N/A"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"status",children:"Status"}),(0,s.jsxs)(z,{value:X,onValueChange:Q,children:[(0,s.jsx)(C,{children:(0,s.jsx)(_,{placeholder:"Set status"})}),(0,s.jsxs)(S,{children:[(0,s.jsx)(E,{value:"pending",children:"Pending"}),(0,s.jsx)(E,{value:"reviewed",children:"Reviewed"}),(0,s.jsx)(E,{value:"approved",children:"Approved"}),(0,s.jsx)(E,{value:"rejected",children:"Rejected"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"notes",children:"Notes"}),(0,s.jsx)(T.T,{id:"notes",value:q,onChange:e=>M(e.target.value),placeholder:"Add internal notes..."})]}),(0,s.jsx)(i.$,{onClick:ee,children:"Update Application"})]})})]}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:(0,s.jsx)("p",{children:"Select an application to view details"})})})]})})]})}),(0,s.jsx)(m,{value:"waitlist",children:(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{children:"Waitlist Entries"}),(0,s.jsx)(n.BT,{children:"Manage users who have joined the waitlist."})]}),(0,s.jsx)(n.Wu,{children:O?(0,s.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,s.jsx)($.A,{className:"h-8 w-8 animate-spin"})}):(0,s.jsxs)(v,{children:[(0,s.jsx)(f,{children:(0,s.jsxs)(b,{children:[(0,s.jsx)(j,{children:"Name"}),(0,s.jsx)(j,{children:"Email"}),(0,s.jsx)(j,{children:"Website"}),(0,s.jsx)(j,{children:"Status"}),(0,s.jsx)(j,{children:"Joined"}),(0,s.jsx)(j,{children:"Actions"})]})}),(0,s.jsx)(g,{children:R.map(e=>(0,s.jsxs)(b,{children:[(0,s.jsx)(w,{children:e.name}),(0,s.jsx)(w,{children:e.email}),(0,s.jsx)(w,{children:(0,s.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:e.website})}),(0,s.jsx)(w,{children:(0,s.jsx)(p.E,{variant:"invited"===e.status?"default":"secondary",children:e.status})}),(0,s.jsx)(w,{children:(0,c.Y)(new Date(e.createdAt))}),(0,s.jsx)(w,{children:"invited"!==e.status&&(0,s.jsxs)(i.$,{size:"sm",onClick:()=>et(e.email),children:[(0,s.jsx)(I.A,{className:"mr-2 h-4 w-4"}),"Send Invite"]})})]},e.id))})]})})]})})]})})]}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background text-foreground p-4",children:(0,s.jsxs)(n.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsxs)(n.ZB,{className:"text-2xl flex items-center",children:[(0,s.jsx)(P.A,{className:"mr-2 h-6 w-6"})," Admin Access"]}),(0,s.jsx)(n.BT,{children:"Enter your secret key to access the dashboard."})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"admin-key",children:"Admin Key"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.p,{id:"admin-key",type:y?"text":"password",value:e,onChange:e=>t(e.target.value),onKeyPress:e=>"Enter"===e.key&&G(),placeholder:"Enter your secret key"}),(0,s.jsx)(i.$,{variant:"ghost",size:"icon",className:"absolute top-1/2 right-2 -translate-y-1/2 h-7 w-7",onClick:()=>N(!y),children:y?(0,s.jsx)(Z.A,{className:"h-4 w-4"}):(0,s.jsx)(L.A,{className:"h-4 w-4"})})]})]}),(0,s.jsx)(i.$,{onClick:G,className:"w-full",children:"Authenticate"})]})})]})})}},5057:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var s=a(5155);a(2115);var r=a(968),n=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},6126:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var s=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let d=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:n=!1,...l}=e,o=n?r.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(d({variant:a}),t),...l})}},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}},6752:(e,t,a)=>{"use strict";a.d(t,{c:()=>m});var s=a(5155);a(2115);var r=a(2098),n=a(3509),i=a(1362),d=a(285),l=a(8698),o=a(9434);function c(e){let{...t}=e;return(0,s.jsx)(l.bL,{"data-slot":"dropdown-menu",...t})}function u(e){let{...t}=e;return(0,s.jsx)(l.l9,{"data-slot":"dropdown-menu-trigger",...t})}function x(e){let{className:t,sideOffset:a=4,...r}=e;return(0,s.jsx)(l.ZL,{children:(0,s.jsx)(l.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function h(e){let{className:t,inset:a,variant:r="default",...n}=e;return(0,s.jsx)(l.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n})}function m(){let{setTheme:e}=(0,i.D)();return(0,s.jsxs)(c,{children:[(0,s.jsx)(u,{asChild:!0,children:(0,s.jsxs)(d.$,{variant:"outline",size:"icon",children:[(0,s.jsx)(r.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(n.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,s.jsxs)(x,{align:"end",children:[(0,s.jsx)(h,{onClick:()=>e("light"),children:"Light"}),(0,s.jsx)(h,{onClick:()=>e("dark"),children:"Dark"}),(0,s.jsx)(h,{onClick:()=>e("system"),children:"System"})]})]})}},8300:(e,t,a)=>{Promise.resolve().then(a.bind(a,3394))},8539:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var s=a(5155);a(2115);var r=a(9434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},9434:(e,t,a)=>{"use strict";a.d(t,{Y:()=>i,cn:()=>n});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function i(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(e)}}},e=>{var t=t=>e(e.s=t);e.O(0,[912,908,427,441,684,358],()=>t(8300)),_N_E=e.O()}]);