/* PayPerCrawl Admin Styles */
:root {
    --pc-primary: #2563eb;
    --pc-success: #16a34a;
    --pc-bg: #f8fafc;
    --pc-text: #1f2937;
    --pc-border: #e5e7eb;
    --pc-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --pc-hover: #1d4ed8;
    --pc-warning: #f59e0b;
    --pc-danger: #dc2626;
}

/* Main Container */
.wrap.paypercrawl {
    background: var(--pc-bg);
    min-height: 100vh;
    padding: 20px;
    color: var(--pc-text);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Early Access Banner */
.early-access-banner {
    background: linear-gradient(135deg, var(--pc-primary), #3b82f6);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: var(--pc-shadow);
    position: relative;
    overflow: hidden;
}

.early-access-banner::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.early-access-banner h2 {
    margin: 0 0 10px 0;
    font-size: 24px;
    font-weight: 600;
}

.early-access-banner p {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
}

.early-access-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 10px;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: var(--pc-shadow);
    border: 1px solid var(--pc-border);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
    margin: 0 0 16px 0;
    color: var(--pc-text);
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.7;
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--pc-primary);
    margin-bottom: 8px;
    display: block;
}

.stat-change {
    font-size: 14px;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--pc-success);
}

.stat-change.negative {
    color: var(--pc-danger);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
}

.stat-icon.detections {
    background: rgba(37, 99, 235, 0.1);
    color: var(--pc-primary);
}

.stat-icon.revenue {
    background: rgba(22, 163, 74, 0.1);
    color: var(--pc-success);
}

.stat-icon.bots {
    background: rgba(245, 158, 11, 0.1);
    color: var(--pc-warning);
}

.stat-icon.accuracy {
    background: rgba(220, 38, 38, 0.1);
    color: var(--pc-danger);
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: var(--pc-shadow);
    border: 1px solid var(--pc-border);
}

.chart-container.large {
    grid-column: span 2;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--pc-border);
}

.chart-header h3 {
    margin: 0;
    color: var(--pc-text);
    font-size: 18px;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 8px;
}

.chart-period {
    padding: 8px 16px;
    border: 1px solid var(--pc-border);
    background: white;
    color: var(--pc-text);
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.chart-period:hover {
    background: var(--pc-bg);
    border-color: var(--pc-primary);
}

.chart-period.active {
    background: var(--pc-primary);
    color: white;
    border-color: var(--pc-primary);
}

.chart-content {
    position: relative;
    height: 400px;
}

/* Analytics Page Specific */
.analytics-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
}

.analytics-summary {
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: var(--pc-shadow);
    border: 1px solid var(--pc-border);
}

.summary-card h3 {
    margin: 0 0 20px 0;
    color: var(--pc-text);
    font-size: 18px;
    font-weight: 600;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-stat {
    text-align: center;
    padding: 16px;
    border: 1px solid var(--pc-border);
    border-radius: 8px;
    background: var(--pc-bg);
}

.stat-label {
    display: block;
    font-size: 14px;
    color: var(--pc-text);
    opacity: 0.7;
    margin-bottom: 8px;
    font-weight: 500;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--pc-primary);
}

/* Heatmap */
.heatmap-section {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: var(--pc-shadow);
    border: 1px solid var(--pc-border);
    margin-bottom: 30px;
}

.heatmap-section h3 {
    margin: 0 0 20px 0;
    color: var(--pc-text);
    font-size: 18px;
    font-weight: 600;
}

.heatmap-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.heatmap-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 4px;
}

.heatmap-hour {
    aspect-ratio: 1;
    background: var(--pc-primary);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.heatmap-hour:hover {
    transform: scale(1.1);
    z-index: 10;
    box-shadow: var(--pc-shadow);
}

.heatmap-legend {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 14px;
    color: var(--pc-text);
}

.legend-gradient {
    width: 100px;
    height: 12px;
    background: linear-gradient(to right, rgba(37, 99, 235, 0.2), var(--pc-primary));
    border-radius: 6px;
}

/* Tables */
.detections-table-section {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: var(--pc-shadow);
    border: 1px solid var(--pc-border);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--pc-border);
}

.section-header h3 {
    margin: 0;
    color: var(--pc-text);
    font-size: 18px;
    font-weight: 600;
}

.table-filters {
    display: flex;
    gap: 12px;
}

.table-filters select {
    padding: 8px 12px;
    border: 1px solid var(--pc-border);
    border-radius: 6px;
    background: white;
    color: var(--pc-text);
    font-size: 14px;
}

.table-container {
    overflow-x: auto;
    border: 1px solid var(--pc-border);
    border-radius: 8px;
}

#detections-table {
    margin: 0;
    border: none;
}

#detections-table th {
    background: var(--pc-bg);
    color: var(--pc-text);
    font-weight: 600;
    border-bottom: 2px solid var(--pc-border);
    padding: 16px 12px;
}

#detections-table th.sortable {
    cursor: pointer;
    user-select: none;
}

#detections-table th.sortable:hover {
    background: #e5e7eb;
}

#detections-table td {
    padding: 12px;
    border-bottom: 1px solid var(--pc-border);
    vertical-align: middle;
}

.confidence-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    min-width: 50px;
    display: inline-block;
}

.confidence-high {
    background: rgba(22, 163, 74, 0.1);
    color: var(--pc-success);
}

.confidence-medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--pc-warning);
}

.confidence-low {
    background: rgba(220, 38, 38, 0.1);
    color: var(--pc-danger);
}

.action-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: capitalize;
}

.action-blocked {
    background: rgba(220, 38, 38, 0.1);
    color: var(--pc-danger);
}

.action-logged {
    background: rgba(37, 99, 235, 0.1);
    color: var(--pc-primary);
}

.action-allowed {
    background: rgba(22, 163, 74, 0.1);
    color: var(--pc-success);
}

.user-agent-cell,
.url-cell {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.url-link {
    color: var(--pc-primary);
    text-decoration: none;
}

.url-link:hover {
    text-decoration: underline;
}

/* Settings Page */
.settings-form {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: var(--pc-shadow);
    border: 1px solid var(--pc-border);
    margin-bottom: 20px;
}

.settings-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--pc-border);
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.settings-section h3 {
    margin: 0 0 16px 0;
    color: var(--pc-text);
    font-size: 18px;
    font-weight: 600;
}

.form-table th {
    color: var(--pc-text);
    font-weight: 600;
    vertical-align: top;
    padding-top: 12px;
}

.form-table input[type="text"],
.form-table input[type="password"],
.form-table select,
.form-table textarea {
    border: 1px solid var(--pc-border);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    color: var(--pc-text);
    background: white;
    transition: border-color 0.2s ease;
}

.form-table input[type="text"]:focus,
.form-table input[type="password"]:focus,
.form-table select:focus,
.form-table textarea:focus {
    border-color: var(--pc-primary);
    box-shadow: 0 0 0 1px var(--pc-primary);
    outline: none;
}

.radio-group {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: 1px solid var(--pc-border);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.radio-option:hover {
    background: var(--pc-bg);
    border-color: var(--pc-primary);
}

.radio-option input[type="radio"]:checked + .radio-content {
    color: var(--pc-primary);
    font-weight: 600;
}

.radio-option input[type="radio"]:checked ~ .radio-option {
    background: rgba(37, 99, 235, 0.05);
    border-color: var(--pc-primary);
}

.test-api-btn {
    background: var(--pc-primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.test-api-btn:hover {
    background: var(--pc-hover);
}

.help-section {
    background: var(--pc-bg);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--pc-border);
}

.help-section h4 {
    margin: 0 0 12px 0;
    color: var(--pc-text);
    font-size: 16px;
    font-weight: 600;
}

.signatures-display {
    background: #f9fafb;
    border: 1px solid var(--pc-border);
    border-radius: 6px;
    max-height: 200px;
    overflow-y: auto;
    padding: 12px;
}

.signature-item {
    padding: 4px 8px;
    background: white;
    border-radius: 4px;
    margin-bottom: 4px;
    font-family: monospace;
    font-size: 12px;
    border: 1px solid #e5e7eb;
}

/* Utilities */
.no-data {
    text-align: center;
    padding: 40px 20px;
    color: var(--pc-text);
    opacity: 0.7;
}

.no-data p {
    margin: 8px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .chart-container.large {
        grid-column: span 1;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .heatmap-grid {
        grid-template-columns: repeat(6, 1fr);
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .table-filters {
        width: 100%;
    }
    
    .table-filters select {
        flex: 1;
    }
    
    .radio-group {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .wrap.paypercrawl {
        padding: 12px;
    }
    
    .early-access-banner {
        padding: 16px;
    }
    
    .summary-stats {
        grid-template-columns: 1fr;
    }
    
    .heatmap-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}
