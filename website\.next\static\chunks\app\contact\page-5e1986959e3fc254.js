(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(5155);t(2115);var a=t(9708),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:i,asChild:o=!1,...l}=e,c=o?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:t,size:i,className:r})),...l})}},968:(e,r,t)=>{"use strict";t.d(r,{b:()=>d});var s=t(2115),a=t(3655),i=t(5155),n=s.forwardRef((e,r)=>(0,i.jsx)(a.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var d=n},2486:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(5155);t(2115);var a=t(9434);function i(e){let{className:r,type:t,...i}=e;return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...i})}},4186:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5057:(e,r,t)=>{"use strict";t.d(r,{J:()=>n});var s=t(5155);t(2115);var a=t(968),i=t(9434);function n(e){let{className:r,...t}=e;return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},5525:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5596:(e,r,t)=>{Promise.resolve().then(t.bind(t,8201))},6126:(e,r,t)=>{"use strict";t.d(r,{E:()=>o});var s=t(5155);t(2115);var a=t(9708),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:r,variant:t,asChild:i=!1,...o}=e,l=i?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:t}),r),...o})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var s=t(5155);t(2115);var a=t(9434);function i(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function n(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function d(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function o(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function l(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}},6752:(e,r,t)=>{"use strict";t.d(r,{c:()=>h});var s=t(5155);t(2115);var a=t(2098),i=t(3509),n=t(1362),d=t(285),o=t(8698),l=t(9434);function c(e){let{...r}=e;return(0,s.jsx)(o.bL,{"data-slot":"dropdown-menu",...r})}function u(e){let{...r}=e;return(0,s.jsx)(o.l9,{"data-slot":"dropdown-menu-trigger",...r})}function m(e){let{className:r,sideOffset:t=4,...a}=e;return(0,s.jsx)(o.ZL,{children:(0,s.jsx)(o.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",r),...a})})}function x(e){let{className:r,inset:t,variant:a="default",...i}=e;return(0,s.jsx)(o.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":a,className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",r),...i})}function h(){let{setTheme:e}=(0,n.D)();return(0,s.jsxs)(c,{children:[(0,s.jsx)(u,{asChild:!0,children:(0,s.jsxs)(d.$,{variant:"outline",size:"icon",children:[(0,s.jsx)(a.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(i.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,s.jsxs)(m,{align:"end",children:[(0,s.jsx)(x,{onClick:()=>e("light"),children:"Light"}),(0,s.jsx)(x,{onClick:()=>e("dark"),children:"Dark"}),(0,s.jsx)(x,{onClick:()=>e("system"),children:"System"})]})]})}},8201:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j});var s=t(5155),a=t(6695),i=t(6126),n=t(285),d=t(2523),o=t(8539),l=t(5057),c=t(5525),u=t(8883),m=t(9946);let x=(0,m.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),h=(0,m.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var p=t(4186),g=t(2486),f=t(6874),v=t.n(f),b=t(6752);function j(){return(0,s.jsxs)("div",{className:"min-h-screen bg-background text-foreground",children:[(0,s.jsx)("header",{className:"sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,s.jsxs)("div",{className:"container flex h-14 items-center",children:[(0,s.jsxs)(v(),{href:"/",className:"mr-6 flex items-center space-x-2",children:[(0,s.jsx)(c.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{className:"font-bold",children:"PayperCrawl"})]}),(0,s.jsxs)("nav",{className:"flex items-center space-x-6 text-sm font-medium",children:[(0,s.jsx)(v(),{href:"/features",className:"text-muted-foreground transition-colors hover:text-foreground",children:"Features"}),(0,s.jsx)(v(),{href:"/about",className:"text-muted-foreground transition-colors hover:text-foreground",children:"About"}),(0,s.jsx)(v(),{href:"/blog",className:"text-muted-foreground transition-colors hover:text-foreground",children:"Blog"}),(0,s.jsx)(v(),{href:"/careers",className:"text-muted-foreground transition-colors hover:text-foreground",children:"Careers"}),(0,s.jsx)(v(),{href:"/contact",className:"text-foreground",children:"Contact"})]}),(0,s.jsxs)("div",{className:"flex flex-1 items-center justify-end space-x-4",children:[(0,s.jsx)(b.c,{}),(0,s.jsx)(n.$,{asChild:!0,children:(0,s.jsx)(v(),{href:"/waitlist",children:"Join Waitlist"})})]})]})}),(0,s.jsx)("section",{className:"relative overflow-hidden bg-primary text-primary-foreground",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)(i.E,{variant:"secondary",className:"mb-4 bg-primary-foreground/20 text-primary-foreground border-primary-foreground/30",children:[(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),"BETA PROGRAM"]}),"Contact Us • 100% Revenue Share"]}),(0,s.jsxs)("h1",{className:"text-4xl lg:text-5xl font-bold mb-6",children:["Apply for Exclusive",(0,s.jsx)("span",{className:"text-primary-foreground/80",children:" Beta Access"})]}),(0,s.jsx)("p",{className:"text-xl text-primary-foreground/90 mb-8 max-w-3xl mx-auto",children:"Join our exclusive invite-only program and get 100% revenue share. Limited spots available for selected WordPress publishers."})]})})}),(0,s.jsx)("section",{className:"py-24",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8 mb-16",children:[(0,s.jsxs)(a.Zp,{className:"bg-card text-card-foreground",children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(u.A,{className:"h-12 w-12 text-primary mb-4 mx-auto"}),(0,s.jsx)(a.ZB,{children:"Email Us"}),(0,s.jsx)(a.BT,{children:"Get in touch with our team"})]}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium",children:"<EMAIL>"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Technical support & plugin help"})]}),(0,s.jsxs)("div",{className:"space-y-2 mt-4",children:[(0,s.jsx)("p",{className:"font-medium",children:"<EMAIL>"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Sales inquiries & partnerships"})]})]})]}),(0,s.jsxs)(a.Zp,{className:"bg-card text-card-foreground",children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(x,{className:"h-12 w-12 text-primary mb-4 mx-auto"}),(0,s.jsx)(a.ZB,{children:"Call Us"}),(0,s.jsx)(a.BT,{children:"Speak with our team directly"})]}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium",children:"+1 (555) 123-4567"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Business & Enterprise customers"})]}),(0,s.jsxs)("div",{className:"space-y-2 mt-4",children:[(0,s.jsx)("p",{className:"font-medium",children:"+1 (555) 987-6543"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Technical support line"})]})]})]}),(0,s.jsxs)(a.Zp,{className:"bg-card text-card-foreground",children:[(0,s.jsxs)(a.aR,{children:[(0,s.jsx)(h,{className:"h-12 w-12 text-primary mb-4 mx-auto"}),(0,s.jsx)(a.ZB,{children:"Our Office"}),(0,s.jsx)(a.BT,{children:"Visit our headquarters"})]}),(0,s.jsxs)(a.Wu,{children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"font-medium",children:"123 Innovation Drive"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Suite 456, Tech City, 78910"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 mt-4 text-sm text-muted-foreground",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Mon-Fri, 9am - 5pm"})]})]})]})]}),(0,s.jsxs)(a.Zp,{className:"max-w-4xl mx-auto bg-card text-card-foreground",children:[(0,s.jsxs)(a.aR,{className:"text-center",children:[(0,s.jsx)(a.ZB,{className:"text-3xl",children:"Send us a message"}),(0,s.jsx)(a.BT,{children:"We'll get back to you within 24 hours."})]}),(0,s.jsx)(a.Wu,{children:(0,s.jsxs)("form",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid sm:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"first-name",children:"First Name"}),(0,s.jsx)(d.p,{id:"first-name",placeholder:"John"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"last-name",children:"Last Name"}),(0,s.jsx)(d.p,{id:"last-name",placeholder:"Doe"})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(d.p,{id:"email",type:"email",placeholder:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"subject",children:"Subject"}),(0,s.jsx)(d.p,{id:"subject",placeholder:"e.g., Beta Program Inquiry"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"message",children:"Message"}),(0,s.jsx)(o.T,{id:"message",placeholder:"Your message...",className:"min-h-[150px]"})]}),(0,s.jsxs)(n.$,{type:"submit",size:"lg",className:"w-full",children:[(0,s.jsx)(g.A,{className:"mr-2 h-5 w-5"}),"Send Message"]})]})})]})]})}),(0,s.jsx)("footer",{className:"border-t border-border/40",children:(0,s.jsxs)("div",{className:"container mx-auto py-6 px-4 md:px-6 flex flex-col md:flex-row justify-between items-center text-sm text-muted-foreground",children:[(0,s.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," PayperCrawl. All rights reserved."]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 mt-4 md:mt-0",children:[(0,s.jsx)(v(),{href:"/contact",className:"hover:text-foreground",children:"Contact"}),(0,s.jsx)(v(),{href:"/admin",className:"hover:text-foreground",children:"Admin"})]})]})})]})}},8539:(e,r,t)=>{"use strict";t.d(r,{T:()=>i});var s=t(5155);t(2115);var a=t(9434);function i(e){let{className:r,...t}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),...t})}},8883:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9434:(e,r,t)=>{"use strict";t.d(r,{Y:()=>n,cn:()=>i});var s=t(2596),a=t(9688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}function n(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(e)}}},e=>{var r=r=>e(e.s=r);e.O(0,[912,908,441,684,358],()=>r(5596)),_N_E=e.O()}]);