1:"$Sreact.fragment"
2:I[1483,["912","static/chunks/912-f15cee8e43eff522.js","177","static/chunks/app/layout-693efb02841753c4.js"],"ThemeProvider"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[639,["912","static/chunks/912-f15cee8e43eff522.js","177","static/chunks/app/layout-693efb02841753c4.js"],"Toaster"]
6:I[894,[],"ClientPageRoot"]
7:I[3280,["912","static/chunks/912-f15cee8e43eff522.js","908","static/chunks/908-d3378bc095cec9c1.js","120","static/chunks/120-14d081a5a76a53f6.js","944","static/chunks/app/features/page-ac011fa0eba629c0.js"],"default"]
a:I[9665,[],"OutletBoundary"]
d:I[4911,[],"AsyncMetadataOutlet"]
f:I[9665,[],"ViewportBoundary"]
11:I[9665,[],"MetadataBoundary"]
13:I[6614,[],""]
:HL["/_next/static/css/f30152c0704fba31.css","style"]
:HL["/_next/static/css/cd673b4ada3b0cf5.css","style"]
0:{"P":null,"b":"PUpg6NEhO1nTebE7UtB2i","p":"","c":["","features",""],"i":false,"f":[[["",{"children":["features",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/f30152c0704fba31.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/cd673b4ada3b0cf5.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased bg-background text-foreground","children":[["$","$L2",null,{"attribute":"class","defaultTheme":"system","enableSystem":true,"disableTransitionOnChange":true,"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$L5",null,{}]]}]}]]}],{"children":["features",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L6",null,{"Component":"$7","searchParams":{},"params":{},"promises":["$@8","$@9"]}],null,["$","$La",null,{"children":["$Lb","$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","EMVSxGgdxbsU1w_-l3Isuv",{"children":[["$","$Lf",null,{"children":"$L10"}],null]}],["$","$L11",null,{"children":"$L12"}]]}],false]],"m":"$undefined","G":["$13","$undefined"],"s":false,"S":true}
14:"$Sreact.suspense"
15:I[4911,[],"AsyncMetadata"]
8:{}
9:{}
12:["$","div",null,{"hidden":true,"children":["$","$14",null,{"fallback":null,"children":["$","$L15",null,{"promise":"$@16"}]}]}]
c:null
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:null
e:{"metadata":[["$","title","0",{"children":"PayPerCrawl - AI Content Monetization Platform"}],["$","meta","1",{"name":"description","content":"Turn AI bot traffic into revenue. PayPerCrawl detects AI bots crawling your content and converts them into paying customers. Built for WordPress with enterprise-grade security."}],["$","meta","2",{"name":"author","content":"PayPerCrawl Team"}],["$","meta","3",{"name":"keywords","content":"AI monetization,WordPress,content protection,bot detection,revenue generation,AI training data"}],["$","meta","4",{"property":"og:title","content":"PayPerCrawl - AI Content Monetization Platform"}],["$","meta","5",{"property":"og:description","content":"Turn AI bot traffic into revenue with advanced bot detection and monetization"}],["$","meta","6",{"property":"og:url","content":"https://paypercrawl.tech/"}],["$","meta","7",{"property":"og:site_name","content":"PayPerCrawl"}],["$","meta","8",{"property":"og:type","content":"website"}],["$","meta","9",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","10",{"name":"twitter:title","content":"PayPerCrawl - AI Content Monetization Platform"}],["$","meta","11",{"name":"twitter:description","content":"Turn AI bot traffic into revenue with advanced bot detection and monetization"}],["$","link","12",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"128x128"}]],"error":null,"digest":"$undefined"}
16:{"metadata":"$e:metadata","error":null,"digest":"$undefined"}
