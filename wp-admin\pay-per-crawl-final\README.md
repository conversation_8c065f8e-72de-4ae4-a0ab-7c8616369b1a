# PayPerCrawl WordPress Plugin

Turn AI bot traffic into revenue with this simple, lightweight WordPress plugin.

## Description

PayPerCrawl detects AI bots visiting your website and helps you monetize this traffic. During the free beta period, you keep 100% of all earnings!

## Features

- **Simple Bot Detection**: Detects major AI bots like <PERSON><PERSON><PERSON>ot, <PERSON><PERSON><PERSON><PERSON>, Google-Extended, and more
- **Real-time Analytics**: Track bot visits, companies, and potential earnings
- **Flexible Actions**: Choose to allow, log, or block bot traffic
- **Revenue Tracking**: Calculate potential earnings from bot detections
- **Free Beta**: Keep 100% of earnings during early access period
- **Easy Setup**: Simple installation with minimal configuration

## Installation

1. Upload the `pay-per-crawl` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to PayPerCrawl in your admin menu to configure settings

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

## Bot Detection

The plugin detects bots using user-agent patterns including:

- **OpenAI**: GP<PERSON><PERSON><PERSON>, ChatGPT-User
- **Anthropic**: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
- **Google**: Google-Extended, GoogleOther
- **Meta**: FacebookBot, Meta-ExternalAgent
- **Bytedance**: Bytespider
- **Perplexity**: PerplexityBot
- **Common AI**: CCBot, anthropic-ai, ChatGPT

## Dashboard Features

### Analytics
- 30-day detection trends
- Bot company distribution
- Hourly detection heatmap
- Detailed detection logs
- CSV export functionality

### Settings
- API configuration
- Bot action preferences (allow/log/block)
- Detection parameters
- Help documentation

## Early Access Benefits

- **100% Revenue Share**: Keep all earnings during beta
- **Priority Support**: Direct access to development team
- **Feature Requests**: Influence future development
- **Beta Pricing**: Lock in early access rates

## Support

For support during the beta period:
- Visit the plugin settings page for documentation
- Check the analytics dashboard for detection status
- Review the detection logs for troubleshooting

## Privacy

This plugin:
- Only logs bot detections, not user data
- Stores minimal information (IP, user-agent, timestamp)
- Follows WordPress privacy best practices
- Respects visitor privacy settings

## Changelog

### 1.0.0-beta
- Initial release
- Basic bot detection
- Analytics dashboard
- Settings configuration
- Early access features

## License

GPL v2 or later

## Author

PayPerCrawl - Turn AI bot traffic into revenue.
