(()=>{var e={};e.id=865,e.ids=[865],e.modules={549:(e,t,r)=>{"use strict";r.d(t,{u:()=>A});var n=Object.defineProperty,i=Object.defineProperties,s=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable,d=(e,t,r)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,u=(e,t)=>{for(var r in t||(t={}))o.call(t,r)&&d(e,r,t[r]);if(a)for(var r of a(t))l.call(t,r)&&d(e,r,t[r]);return e},c=(e,t)=>i(e,s(t)),h=(e,t,r)=>new Promise((n,i)=>{var s=e=>{try{o(r.next(e))}catch(e){i(e)}},a=e=>{try{o(r.throw(e))}catch(e){i(e)}},o=e=>e.done?n(e.value):Promise.resolve(e.value).then(s,a);o((r=r.apply(e,t)).next())}),p=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/api-keys",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/api-keys")})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/api-keys/${e}`)})}},m=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/audiences",e,t)})}list(){return h(this,null,function*(){return yield this.resend.get("/audiences")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/audiences/${e}`)})}};function y(e){var t;return{attachments:null==(t=e.attachments)?void 0:t.map(e=>({content:e.content,filename:e.filename,path:e.path,content_type:e.contentType,inline_content_id:e.inlineContentId})),bcc:e.bcc,cc:e.cc,from:e.from,headers:e.headers,html:e.html,reply_to:e.replyTo,scheduled_at:e.scheduledAt,subject:e.subject,tags:e.tags,text:e.text,to:e.to}}var f=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){let n=[];for(let t of e){if(t.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(794).then(r.bind(r,3794));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}t.html=yield this.renderAsync(t.react),t.react=void 0}n.push(y(t))}return yield this.resend.post("/emails/batch",n,t)})}},g=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(794).then(r.bind(r,3794));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/broadcasts",{name:e.name,audience_id:e.audienceId,preview_text:e.previewText,from:e.from,html:e.html,reply_to:e.replyTo,subject:e.subject,text:e.text},t)})}send(e,t){return h(this,null,function*(){return yield this.resend.post(`/broadcasts/${e}/send`,{scheduled_at:null==t?void 0:t.scheduledAt})})}list(){return h(this,null,function*(){return yield this.resend.get("/broadcasts")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/broadcasts/${e}`)})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/broadcasts/${e}`)})}update(e,t){return h(this,null,function*(){return yield this.resend.patch(`/broadcasts/${e}`,{name:t.name,audience_id:t.audienceId,from:t.from,html:t.html,text:t.text,subject:t.subject,reply_to:t.replyTo,preview_text:t.previewText})})}},b=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post(`/audiences/${e.audienceId}/contacts`,{unsubscribed:e.unsubscribed,email:e.email,first_name:e.firstName,last_name:e.lastName},t)})}list(e){return h(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts`)})}get(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.get(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}update(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.patch(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`,{unsubscribed:e.unsubscribed,first_name:e.firstName,last_name:e.lastName}):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}remove(e){return h(this,null,function*(){return e.id||e.email?yield this.resend.delete(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}},w=class{constructor(e){this.resend=e}create(e){return h(this,arguments,function*(e,t={}){return yield this.resend.post("/domains",{name:e.name,region:e.region,custom_return_path:e.customReturnPath},t)})}list(){return h(this,null,function*(){return yield this.resend.get("/domains")})}get(e){return h(this,null,function*(){return yield this.resend.get(`/domains/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/domains/${e.id}`,{click_tracking:e.clickTracking,open_tracking:e.openTracking,tls:e.tls})})}remove(e){return h(this,null,function*(){return yield this.resend.delete(`/domains/${e}`)})}verify(e){return h(this,null,function*(){return yield this.resend.post(`/domains/${e}/verify`)})}},v=class{constructor(e){this.resend=e}send(e){return h(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return h(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(794).then(r.bind(r,3794));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/emails",y(e),t)})}get(e){return h(this,null,function*(){return yield this.resend.get(`/emails/${e}`)})}update(e){return h(this,null,function*(){return yield this.resend.patch(`/emails/${e.id}`,{scheduled_at:e.scheduledAt})})}cancel(e){return h(this,null,function*(){return yield this.resend.post(`/emails/${e}/cancel`)})}},x="undefined"!=typeof process&&process.env&&process.env.RESEND_BASE_URL||"https://api.resend.com",P="undefined"!=typeof process&&process.env&&process.env.RESEND_USER_AGENT||"resend-node:4.8.0",A=class{constructor(e){if(this.key=e,this.apiKeys=new p(this),this.audiences=new m(this),this.batch=new f(this),this.broadcasts=new g(this),this.contacts=new b(this),this.domains=new w(this),this.emails=new v(this),!e&&("undefined"!=typeof process&&process.env&&(this.key=process.env.RESEND_API_KEY),!this.key))throw Error('Missing API key. Pass it to the constructor `new Resend("re_123")`');this.headers=new Headers({Authorization:`Bearer ${this.key}`,"User-Agent":P,"Content-Type":"application/json"})}fetchRequest(e){return h(this,arguments,function*(e,t={}){try{let r=yield fetch(`${x}${e}`,t);if(!r.ok)try{let e=yield r.text();return{data:null,error:JSON.parse(e)}}catch(t){if(t instanceof SyntaxError)return{data:null,error:{name:"application_error",message:"Internal server error. We are unable to process your request right now, please try again later."}};let e={message:r.statusText,name:"application_error"};if(t instanceof Error)return{data:null,error:c(u({},e),{message:t.message})};return{data:null,error:e}}return{data:yield r.json(),error:null}}catch(e){return{data:null,error:{name:"application_error",message:"Unable to fetch data. The request could not be resolved."}}}})}post(e,t){return h(this,arguments,function*(e,t,r={}){let n=new Headers(this.headers);r.idempotencyKey&&n.set("Idempotency-Key",r.idempotencyKey);let i=u({method:"POST",headers:n,body:JSON.stringify(t)},r);return this.fetchRequest(e,i)})}get(e){return h(this,arguments,function*(e,t={}){let r=u({method:"GET",headers:this.headers},t);return this.fetchRequest(e,r)})}put(e,t){return h(this,arguments,function*(e,t,r={}){let n=u({method:"PUT",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,n)})}patch(e,t){return h(this,arguments,function*(e,t,r={}){let n=u({method:"PATCH",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,n)})}delete(e,t){return h(this,null,function*(){let r={method:"DELETE",headers:this.headers,body:JSON.stringify(t)};return this.fetchRequest(e,r)})}}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1111:(e,t,r)=>{"use strict";r.d(t,{F7:()=>l,G1:()=>o,M:()=>u,YL:()=>d});var n=r(549),i=r(1678);let s=new n.u(process.env.RESEND_API_KEY);async function a({to:e,subject:t,html:r,from:n="PayPerCrawl <<EMAIL>>"}){try{let{data:a,error:o}=await s.emails.send({from:n,to:e,subject:t,html:r});if(await i.db.emailLog.create({data:{to:e,subject:t,body:r,status:o?"failed":"sent",provider:"resend"}}),o)throw console.error("Email send error:",o),console.error("Error details:",JSON.stringify(o,null,2)),Error(`Failed to send email: ${o.message||JSON.stringify(o)}`);return a}catch(n){throw console.error("Email service error:",n),await i.db.emailLog.create({data:{to:e,subject:t,body:r,status:"failed",provider:"resend"}}),n}}async function o(e,t){return a({to:e,subject:"Application Received - PayPerCrawl Beta",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Application Received</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2563eb;">Thank you for your application!</h1>
        
        <p>Hi ${t},</p>
        
        <p>We've received your application for the PayPerCrawl beta program. Our team will review your application and get back to you within 2-3 business days.</p>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">What's Next?</h3>
          <ul>
            <li>Our team will review your application</li>
            <li>We'll contact you if we need additional information</li>
            <li>Selected candidates will receive beta access instructions</li>
          </ul>
        </div>
        
        <p>In the meantime, feel free to explore our website and learn more about how PayPerCrawl can help monetize your AI content.</p>
        
        <p>Best regards,<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function l(e,t,r){return a({to:e,subject:"Welcome to PayPerCrawl Waitlist!",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Waitlist Confirmation</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2563eb;">You're on the waitlist! 🎉</h1>
        
        <p>Hi ${t},</p>
        
        <p>Thank you for joining the PayPerCrawl waitlist! You're currently <strong>#${r}</strong> in line.</p>
        
        <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb;">
          <h3 style="margin-top: 0; color: #2563eb;">Your Position: #${r}</h3>
          <p style="margin-bottom: 0;">We'll notify you as soon as beta access becomes available!</p>
        </div>
        
        <p>While you wait, here's what you can do:</p>
        <ul>
          <li>Follow us on social media for updates</li>
          <li>Share PayPerCrawl with friends who create AI content</li>
          <li>Read our blog for AI monetization insights</li>
        </ul>
        
        <p>We're working hard to launch the beta and can't wait to help you monetize your AI content!</p>
        
        <p>Best regards,<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function d(e,t,r){let n=`https://paypercrawl.tech/?invited=true&token=${r}`;return a({to:e,subject:"Your PayPerCrawl Beta Access is Ready! \uD83D\uDE80",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Beta Access Ready</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #16a34a;">Welcome to PayPerCrawl Beta! 🚀</h1>
        
        <p>Hi ${t},</p>
        
        <p>Congratulations! You've been selected for the PayPerCrawl beta program. You can now start monetizing your AI content!</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${n}" style="background: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
            Access Beta Dashboard
          </a>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #16a34a;">
          <h3 style="margin-top: 0; color: #16a34a;">Getting Started</h3>
          <ol>
            <li>Click the button above to access your beta dashboard</li>
            <li>Install the PayPerCrawl WordPress plugin</li>
            <li>Configure your monetization settings</li>
            <li>Start earning from AI bot traffic!</li>
          </ol>
        </div>
        
        <p>Need help? Our support team is standing by to assist you with setup and configuration.</p>
        
        <p>Welcome aboard!<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function u(e,t,r,n){let i=`New Contact Form Submission: ${r}`,s=`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Contact Form Submission</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #dc2626;">New Contact Form Submission</h1>
        
        <div style="background: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Name:</strong> ${e}</p>
          <p><strong>Email:</strong> ${t}</p>
          <p><strong>Subject:</strong> ${r}</p>
          <p><strong>Message:</strong></p>
          <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
            ${n.replace(/\n/g,"<br>")}
          </div>
        </div>
        
        <p>Please respond to this inquiry promptly.</p>
      </div>
    </body>
    </html>
  `;return a({to:process.env.ADMIN_EMAIL||"<EMAIL>",subject:i,html:s})}},1678:(e,t,r)=>{"use strict";r.d(t,{db:()=>i});let n=require("@prisma/client"),i=globalThis.prisma??new n.PrismaClient({log:["query"]})},2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3505:e=>{"use strict";e.exports=import("prettier/standalone")},4220:(e,t,r)=>{"use strict";r.d(t,{g:()=>d});var n=r(5511);let i={randomUUID:n.randomUUID},s=new Uint8Array(256),a=s.length,o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));let l=function(e,t,r){if(i.randomUUID&&!t&&!e)return i.randomUUID();let l=(e=e||{}).random??e.rng?.()??(a>s.length-16&&((0,n.randomFillSync)(s),a=0),s.slice(a,a+=16));if(l.length<16)throw Error("Random bytes length must be >= 16");if(l[6]=15&l[6]|64,l[8]=63&l[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=l[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(l)};function d(){return`invite_${l().replace(/-/g,"")}`}},4297:e=>{"use strict";e.exports=require("async_hooks")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},7075:e=>{"use strict";e.exports=require("node:stream")},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9774:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>f});var n={};r.r(n),r.d(n,{GET:()=>h,POST:()=>p});var i=r(6559),s=r(8088),a=r(7719),o=r(2190),l=r(1678),d=r(1111),u=r(4220);function c(e){let t=e.headers.get("authorization");return t?.replace("Bearer ","")===process.env.ADMIN_API_KEY}async function h(e){try{if(!c(e))return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),r=t.get("status"),n=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"10"),s=(n-1)*i,a=r?{status:r}:{},[d,u]=await Promise.all([l.db.waitlistEntry.findMany({where:a,orderBy:{createdAt:"desc"},skip:s,take:i}),l.db.waitlistEntry.count({where:a})]);return o.NextResponse.json({waitlistEntries:d,pagination:{page:n,limit:i,total:u,pages:Math.ceil(u/i)}})}catch(e){return console.error("Error fetching waitlist:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e){try{if(!c(e))return o.NextResponse.json({error:"Unauthorized"},{status:401});let{action:t,email:r,emails:n}=await e.json();if("invite"===t){if(r){let e=await l.db.waitlistEntry.findUnique({where:{email:r}});if(!e)return o.NextResponse.json({error:"Email not found on waitlist"},{status:404});if("invited"===e.status)return o.NextResponse.json({error:"User already invited"},{status:400});let t=e.inviteToken||(0,u.g)(),n=await l.db.waitlistEntry.update({where:{email:r},data:{status:"invited",inviteToken:t,invitedAt:new Date}});try{await (0,d.YL)(r,e.name,n.inviteToken)}catch(e){console.error("Failed to send beta invite:",e)}return o.NextResponse.json({message:"Beta invite sent successfully",entry:n})}else if(n&&Array.isArray(n)){let e=[];for(let t of n)try{let r=await l.db.waitlistEntry.findUnique({where:{email:t}});if(!r||"invited"===r.status)continue;let n=r.inviteToken||(0,u.g)(),i=await l.db.waitlistEntry.update({where:{email:t},data:{status:"invited",inviteToken:n,invitedAt:new Date}});try{await (0,d.YL)(t,r.name,i.inviteToken),e.push({email:t,success:!0})}catch(r){console.error(`Failed to send beta invite to ${t}:`,r),e.push({email:t,success:!1,error:"Email failed"})}}catch(r){e.push({email:t,success:!1,error:"Database error"})}return o.NextResponse.json({message:"Bulk invite completed",results:e})}}return o.NextResponse.json({error:"Invalid action or missing parameters"},{status:400})}catch(e){return console.error("Error processing waitlist action:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/admin/waitlist/route",pathname:"/api/admin/waitlist",filename:"route",bundlePath:"app/api/admin/waitlist/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\admin\\waitlist\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:y,workUnitAsyncStorage:f,serverHooks:g}=m;function b(){return(0,a.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:f})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,580],()=>r(9774));module.exports=n})();