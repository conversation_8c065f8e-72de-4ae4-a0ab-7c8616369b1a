{"name": "@arbiter/frontend", "version": "1.0.0", "description": "Arbiter Platform Frontend Applications", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "vite": "^4.5.0", "vitest": "^0.34.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.31"}}