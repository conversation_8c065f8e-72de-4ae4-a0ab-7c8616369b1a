(()=>{var e={};e.id=280,e.ids=[280],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1111:(e,t,r)=>{"use strict";r.d(t,{F7:()=>l,G1:()=>s,M:()=>d,YL:()=>p});var a=r(549),i=r(1678);let o=new a.u(process.env.RESEND_API_KEY);async function n({to:e,subject:t,html:r,from:a="PayPerCrawl <<EMAIL>>"}){try{let{data:n,error:s}=await o.emails.send({from:a,to:e,subject:t,html:r});if(await i.db.emailLog.create({data:{to:e,subject:t,body:r,status:s?"failed":"sent",provider:"resend"}}),s)throw console.error("Email send error:",s),console.error("Error details:",JSON.stringify(s,null,2)),Error(`Failed to send email: ${s.message||JSON.stringify(s)}`);return n}catch(a){throw console.error("Email service error:",a),await i.db.emailLog.create({data:{to:e,subject:t,body:r,status:"failed",provider:"resend"}}),a}}async function s(e,t){return n({to:e,subject:"Application Received - PayPerCrawl Beta",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Application Received</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2563eb;">Thank you for your application!</h1>
        
        <p>Hi ${t},</p>
        
        <p>We've received your application for the PayPerCrawl beta program. Our team will review your application and get back to you within 2-3 business days.</p>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">What's Next?</h3>
          <ul>
            <li>Our team will review your application</li>
            <li>We'll contact you if we need additional information</li>
            <li>Selected candidates will receive beta access instructions</li>
          </ul>
        </div>
        
        <p>In the meantime, feel free to explore our website and learn more about how PayPerCrawl can help monetize your AI content.</p>
        
        <p>Best regards,<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function l(e,t,r){return n({to:e,subject:"Welcome to PayPerCrawl Waitlist!",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Waitlist Confirmation</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2563eb;">You're on the waitlist! 🎉</h1>
        
        <p>Hi ${t},</p>
        
        <p>Thank you for joining the PayPerCrawl waitlist! You're currently <strong>#${r}</strong> in line.</p>
        
        <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb;">
          <h3 style="margin-top: 0; color: #2563eb;">Your Position: #${r}</h3>
          <p style="margin-bottom: 0;">We'll notify you as soon as beta access becomes available!</p>
        </div>
        
        <p>While you wait, here's what you can do:</p>
        <ul>
          <li>Follow us on social media for updates</li>
          <li>Share PayPerCrawl with friends who create AI content</li>
          <li>Read our blog for AI monetization insights</li>
        </ul>
        
        <p>We're working hard to launch the beta and can't wait to help you monetize your AI content!</p>
        
        <p>Best regards,<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function p(e,t,r){let a=`https://paypercrawl.tech/?invited=true&token=${r}`;return n({to:e,subject:"Your PayPerCrawl Beta Access is Ready! \uD83D\uDE80",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Beta Access Ready</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #16a34a;">Welcome to PayPerCrawl Beta! 🚀</h1>
        
        <p>Hi ${t},</p>
        
        <p>Congratulations! You've been selected for the PayPerCrawl beta program. You can now start monetizing your AI content!</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${a}" style="background: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
            Access Beta Dashboard
          </a>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #16a34a;">
          <h3 style="margin-top: 0; color: #16a34a;">Getting Started</h3>
          <ol>
            <li>Click the button above to access your beta dashboard</li>
            <li>Install the PayPerCrawl WordPress plugin</li>
            <li>Configure your monetization settings</li>
            <li>Start earning from AI bot traffic!</li>
          </ol>
        </div>
        
        <p>Need help? Our support team is standing by to assist you with setup and configuration.</p>
        
        <p>Welcome aboard!<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function d(e,t,r,a){let i=`New Contact Form Submission: ${r}`,o=`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Contact Form Submission</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #dc2626;">New Contact Form Submission</h1>
        
        <div style="background: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Name:</strong> ${e}</p>
          <p><strong>Email:</strong> ${t}</p>
          <p><strong>Subject:</strong> ${r}</p>
          <p><strong>Message:</strong></p>
          <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
            ${a.replace(/\n/g,"<br>")}
          </div>
        </div>
        
        <p>Please respond to this inquiry promptly.</p>
      </div>
    </body>
    </html>
  `;return n({to:process.env.ADMIN_EMAIL||"<EMAIL>",subject:i,html:o})}},1678:(e,t,r)=>{"use strict";r.d(t,{db:()=>i});let a=require("@prisma/client"),i=globalThis.prisma??new a.PrismaClient({log:["query"]})},2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3505:e=>{"use strict";e.exports=import("prettier/standalone")},4220:(e,t,r)=>{"use strict";r.d(t,{g:()=>p});var a=r(5511);let i={randomUUID:a.randomUUID},o=new Uint8Array(256),n=o.length,s=[];for(let e=0;e<256;++e)s.push((e+256).toString(16).slice(1));let l=function(e,t,r){if(i.randomUUID&&!t&&!e)return i.randomUUID();let l=(e=e||{}).random??e.rng?.()??(n>o.length-16&&((0,a.randomFillSync)(o),n=0),o.slice(n,n+=16));if(l.length<16)throw Error("Random bytes length must be >= 16");if(l[6]=15&l[6]|64,l[8]=63&l[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=l[e];return t}return function(e,t=0){return(s[e[t+0]]+s[e[t+1]]+s[e[t+2]]+s[e[t+3]]+"-"+s[e[t+4]]+s[e[t+5]]+"-"+s[e[t+6]]+s[e[t+7]]+"-"+s[e[t+8]]+s[e[t+9]]+"-"+s[e[t+10]]+s[e[t+11]]+s[e[t+12]]+s[e[t+13]]+s[e[t+14]]+s[e[t+15]]).toLowerCase()}(l)};function p(){return`invite_${l().replace(/-/g,"")}`}},4297:e=>{"use strict";e.exports=require("async_hooks")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6192:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>b,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>w});var a={};r.r(a),r.d(a,{GET:()=>g,POST:()=>h});var i=r(6559),o=r(8088),n=r(7719),s=r(2190),l=r(1678),p=r(639),d=r(4250),c=r(4220),u=r(1111);let m=p.Ik({name:p.Yj().min(2,"Name must be at least 2 characters"),email:p.Yj().email("Invalid email address"),website:p.Yj().url().optional().or(p.eu("")),companySize:p.k5(["small","medium","large"]).optional(),useCase:p.Yj().optional()});async function h(e){try{let t=await e.json(),r=m.parse(t);if(await l.db.waitlistEntry.findUnique({where:{email:r.email}}))return s.NextResponse.json({error:"This email is already on the waitlist"},{status:400});await l.db.waitlistEntry.create({data:{name:r.name,email:r.email,website:r.website||null,companySize:r.companySize||null,useCase:r.useCase||null,inviteToken:(0,c.g)()}});let a=await y(r.email);try{await (0,u.F7)(r.email,r.name,a)}catch(e){console.error("Failed to send confirmation email:",e)}return s.NextResponse.json({message:"Successfully joined waitlist",position:a})}catch(e){if(console.error("Waitlist join error:",e),e instanceof d.G)return s.NextResponse.json({error:"Validation failed",details:e.issues},{status:400});return s.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e){let t=await l.db.waitlistEntry.findUnique({where:{email:e}});return t?await l.db.waitlistEntry.count({where:{createdAt:{lte:t.createdAt}}}):0}async function g(){try{let e=await l.db.waitlistEntry.findMany({orderBy:{createdAt:"desc"}});return s.NextResponse.json(e)}catch(e){return console.error("Error fetching waitlist:",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}let b=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/waitlist/join/route",pathname:"/api/waitlist/join",filename:"route",bundlePath:"app/api/waitlist/join/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\waitlist\\join\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:w,serverHooks:x}=b;function v(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:w})}},6487:()=>{},7075:e=>{"use strict";e.exports=require("node:stream")},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,580,987],()=>r(6192));module.exports=a})();