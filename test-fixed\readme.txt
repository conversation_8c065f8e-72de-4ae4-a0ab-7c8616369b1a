=== Pay Per Crawl ===
Contributors: paypercrawl
Tags: ai, bot detection, monetization, crawlers, revenue
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 3.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Turn every AI bot crawl into revenue with advanced bot detection and monetization.

== Description ==

**Pay Per Crawl** is the ultimate WordPress plugin for monetizing AI bot traffic on your website. Instead of just blocking bots, turn them into a revenue stream!

= Key Features =

* **30+ AI Bot Signatures** - Detects ChatGPT, Claude, Bard, Copilot, and more
* **Real-time Revenue Tracking** - See earnings from $0.02-$0.12 per bot visit
* **Professional Dashboard** - Modern analytics with Chart.js integration
* **Company Attribution** - Track which AI companies are crawling your content

== Installation ==

1. Upload the plugin files to `/wp-content/plugins/pay-per-crawl-plugin/` directory
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Go to 'Pay Per Crawl' in your admin menu to view the dashboard

== Changelog ==

= 3.0.0 =
* Complete rewrite and rebranding
* 30+ enhanced bot signatures
* Modern dashboard with real-time updates
