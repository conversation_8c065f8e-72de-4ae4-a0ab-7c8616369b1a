=== CrawlGuard WP ===
Contributors: crawlguardteam
Tags: ai, bot detection, monetization, content protection, artificial intelligence
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Turn AI bot traffic into revenue with intelligent content protection and monetization.

== Description ==

CrawlGuard WP is the first WordPress plugin specifically designed to monetize AI bot traffic. While other solutions focus on blocking AI crawlers, we enable content creators to generate revenue from AI companies accessing their content for training and inference.

= Key Features =

* **AI Bot Detection**: 95%+ accuracy with 23+ known AI bot signatures including OpenAI GPTBot, Anthropic Claude, Google Bard, and more
* **Revenue Generation**: Automatic monetization of AI bot requests with dynamic pricing
* **Zero Performance Impact**: Headless architecture via Cloudflare Workers ensures no slowdown to your WordPress site
* **Real-time Analytics**: Comprehensive dashboard with revenue insights and bot detection statistics
* **Enterprise Security**: ACID-compliant financial transactions with robust security measures
* **Global Scale**: Built on Cloudflare's edge network for worldwide performance

= How It Works =

1. **Detection**: Plugin detects AI bots visiting your WordPress site with 95%+ accuracy
2. **Analysis**: Advanced algorithms analyze bot signatures, IP addresses, and behavioral patterns
3. **Monetization**: Detected AI bots are charged based on content type and company-specific rates
4. **Revenue**: Automatic payment processing and revenue sharing via Stripe Connect
5. **Analytics**: Real-time dashboard shows bot activity, revenue generated, and performance metrics

= Supported AI Bots =

* OpenAI (GPTBot, ChatGPT-User)
* Anthropic (Claude-Web, Anthropic-AI)
* Google (Bard, Google-Extended)
* Common Crawl (CCBot)
* Perplexity (PerplexityBot)
* ByteDance (ByteSpider)
* And many more...

= Pricing Tiers =

* **Free**: Basic bot detection and analytics (100 requests/hour)
* **Pro ($15/month)**: Full monetization + Stripe integration (1000 requests/hour)
* **Business ($50/month)**: Multi-site + advanced features (5000 requests/hour)

= Revenue Model =

* 15-25% transaction fee on monetized bot requests
* Automatic revenue sharing via Stripe Connect
* No upfront costs - you only pay when you earn

== Installation ==

= Automatic Installation =

1. Log in to your WordPress admin panel
2. Go to Plugins → Add New
3. Search for "CrawlGuard WP"
4. Click "Install Now" and then "Activate"

= Manual Installation =

1. Download the plugin zip file
2. Go to Plugins → Add New → Upload Plugin
3. Choose the downloaded zip file and click "Install Now"
4. Activate the plugin

= Setup =

1. Go to CrawlGuard in your WordPress admin menu
2. Click "Generate API Key" to create your unique API key
3. Test the connection to ensure everything is working
4. Enable monetization when ready to start earning revenue

== Frequently Asked Questions ==

= Does this plugin slow down my website? =

No! CrawlGuard WP uses a headless architecture where all heavy processing is done on Cloudflare's global edge network. The WordPress plugin adds less than 10ms to your page load time.

= How accurate is the bot detection? =

Our AI bot detection achieves 95%+ accuracy for known AI bots like OpenAI's GPTBot, Anthropic's Claude, and Google's Bard. We continuously update our detection signatures to stay ahead of new bots.

= How much revenue can I expect? =

Revenue depends on your site's traffic and content quality. Typical rates range from $0.001-$0.002 per bot request. A site with 1000 AI bot visits per month could earn $1-2 monthly.

= Do I need a Stripe account? =

Yes, for monetization features you'll need a Stripe business account. The free tier works without Stripe and provides bot detection and analytics.

= Will this affect search engine bots? =

No, CrawlGuard WP specifically targets AI training bots while allowing legitimate search engine bots (Google, Bing, etc.) to crawl your site normally.

= Is my data secure? =

Yes, we follow enterprise-grade security practices including TLS 1.3 encryption, minimal data collection, and GDPR compliance. We never store sensitive personal information.

== Screenshots ==

1. Main dashboard showing bot detection statistics and revenue analytics
2. Real-time bot detection logs with confidence scores and bot identification
3. Revenue analytics with charts showing earnings by AI company
4. Settings page for API configuration and monetization options
5. Bot detection in action with detailed analysis results

== Changelog ==

= 1.0.0 =
* Initial release
* AI bot detection with 95%+ accuracy
* Support for 23+ known AI bot signatures
* Real-time analytics dashboard
* Stripe Connect integration for payments
* Cloudflare Workers backend for global performance
* WordPress 5.0+ and PHP 7.4+ compatibility
* Enterprise-grade security implementation
* Multi-site support
* Comprehensive documentation

== Upgrade Notice ==

= 1.0.0 =
Initial release of CrawlGuard WP. Install now to start monetizing your AI bot traffic!

== Technical Requirements ==

* WordPress 5.0 or higher
* PHP 7.4 or higher
* MySQL 5.6 or higher
* cURL support enabled
* JSON support enabled
* HTTPS recommended for security

== Support ==

For support, documentation, and feature requests:

* Documentation: https://docs.crawlguard.com
* Support: https://creativeinteriorsstudio.com/support
* GitHub: https://github.com/crawlguard/crawlguard-wp

== Privacy Policy ==

CrawlGuard WP collects minimal data required for bot detection:
* User agent strings (for bot identification)
* IP addresses (hashed for privacy)
* Page URLs (for content analysis)
* Timestamps (for analytics)

We do not collect personal information, browsing history, or sensitive data. All data is encrypted in transit and stored securely. For full privacy policy, visit: https://creativeinteriorsstudio.com/privacy

== License ==

This plugin is licensed under the GPL v2 or later.

This program is free software; you can redistribute it and/or modify it under the terms of the GNU General Public License as published by the Free Software Foundation; either version 2 of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for more details.
