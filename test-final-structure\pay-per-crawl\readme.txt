=== Pay Per Crawl ===
Contributors: paypercrawl
Tags: ai, bot-detection, monetization
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 3.0.0
License: GPLv2 or later

Turn every AI bot crawl into revenue with advanced bot detection.

== Description ==

Pay Per Crawl detects AI bots like ChatGPT, Claude, and GPTBot visiting your website and tracks revenue from each detection.

== Installation ==

1. Upload the plugin files to `/wp-content/plugins/pay-per-crawl/`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Go to Pay Per Crawl in your admin menu

== Changelog ==

= 3.0.0 =
* Initial release
