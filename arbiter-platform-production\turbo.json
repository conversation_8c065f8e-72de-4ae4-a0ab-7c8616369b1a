{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"]}, "type-check": {"dependsOn": ["^type-check"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "clean": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:migrate": {"cache": false}, "db:studio": {"cache": false, "persistent": true}}}