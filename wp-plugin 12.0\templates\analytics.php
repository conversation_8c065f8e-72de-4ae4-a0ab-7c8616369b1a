<?php
/**
 * Analytics Template
 * 
 * @package PayPerCrawl
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Handle CSV export
if (isset($_GET['export']) && $_GET['export'] === 'csv' && wp_verify_nonce($_GET['nonce'], 'paypercrawl_export')) {
    $analytics = PayPerCrawl_Analytics::get_instance();
    $analytics->generate_csv_export(30);
}

// Get analytics data
$analytics = null;
$stats = null;
$recent_detections = array();

try {
    if (class_exists('PayPerCrawl_Analytics')) {
        $analytics = PayPerCrawl_Analytics::get_instance();
        $stats = $analytics->get_dashboard_stats();
        $recent_detections = $analytics->get_recent_detections(50);
    }
} catch (Exception $e) {
    // Fallback data
    $stats = (object) array(
        'total' => (object) array('count' => 0, 'unique_ips' => 0, 'unique_bots' => 0),
        'top_bots' => array()
    );
}
?>

<div class="wrap paypercrawl-analytics">
    <h1 class="wp-heading-inline">
        <span class="dashicons dashicons-chart-line"></span>
        PayPerCrawl Analytics
    </h1>
    
    <div class="analytics-actions">
        <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=paypercrawl-analytics&export=csv'), 'paypercrawl_export', 'nonce'); ?>" 
           class="button button-secondary">
            <span class="dashicons dashicons-download"></span> Export CSV
        </a>
        <button type="button" class="button button-secondary" onclick="location.reload()">
            <span class="dashicons dashicons-update"></span> Refresh Data
        </button>
    </div>
    
    <!-- Summary Stats -->
    <div class="analytics-summary">
        <div class="summary-card">
            <h3>30-Day Summary</h3>
            <div class="summary-stats">
                <div class="summary-stat">
                    <span class="stat-label">Total Detections</span>
                    <span class="stat-value"><?php echo number_format($stats['total']->count); ?></span>
                </div>
                <div class="summary-stat">
                    <span class="stat-label">Unique IPs</span>
                    <span class="stat-value"><?php echo number_format($stats['total']->unique_ips); ?></span>
                </div>
                <div class="summary-stat">
                    <span class="stat-label">Bot Companies</span>
                    <span class="stat-value"><?php echo number_format($stats['total']->unique_bots); ?></span>
                </div>
                <div class="summary-stat">
                    <span class="stat-label">Potential Earnings</span>
                    <span class="stat-value">$<?php echo number_format($analytics->calculate_potential_earnings($stats['total']->count), 2); ?></span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts Section -->
    <div class="charts-section">
        <div class="chart-container large">
            <div class="chart-header">
                <h3>Detection Trends (30 Days)</h3>
                <div class="chart-controls">
                    <button type="button" class="chart-period active" data-period="7">7 Days</button>
                    <button type="button" class="chart-period" data-period="30">30 Days</button>
                    <button type="button" class="chart-period" data-period="90">90 Days</button>
                </div>
            </div>
            <div class="chart-content">
                <canvas id="trends-chart" width="800" height="400"></canvas>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-header">
                <h3>Bot Companies Distribution</h3>
            </div>
            <div class="chart-content">
                <canvas id="companies-chart" width="400" height="400"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Heatmap Section -->
    <div class="heatmap-section">
        <h3>Detection Heatmap (24 Hours)</h3>
        <div class="heatmap-container">
            <div class="heatmap-grid" id="detection-heatmap">
                <!-- Heatmap will be generated by JavaScript -->
            </div>
            <div class="heatmap-legend">
                <span>Low</span>
                <div class="legend-gradient"></div>
                <span>High</span>
            </div>
        </div>
    </div>
    
    <!-- Detailed Detections Table -->
    <div class="detections-table-section">
        <div class="section-header">
            <h3>All Detections</h3>
            <div class="table-filters">
                <select id="company-filter">
                    <option value="">All Companies</option>
                    <?php foreach ($stats['top_bots'] as $bot): ?>
                    <option value="<?php echo esc_attr($bot->bot_company); ?>"><?php echo esc_html($bot->bot_company); ?></option>
                    <?php endforeach; ?>
                </select>
                <select id="confidence-filter">
                    <option value="">All Confidence Levels</option>
                    <option value="high">High (80%+)</option>
                    <option value="medium">Medium (60-79%)</option>
                    <option value="low">Low (0-59%)</option>
                </select>
            </div>
        </div>
        
        <?php if (!empty($recent_detections)): ?>
        <div class="table-container">
            <table class="wp-list-table widefat fixed striped" id="detections-table">
                <thead>
                    <tr>
                        <th class="sortable" data-sort="timestamp">Timestamp</th>
                        <th class="sortable" data-sort="company">Bot Company</th>
                        <th class="sortable" data-sort="ip">IP Address</th>
                        <th class="sortable" data-sort="confidence">Confidence</th>
                        <th>User Agent</th>
                        <th>URL</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_detections as $detection): ?>
                    <tr data-company="<?php echo esc_attr($detection->bot_company); ?>" 
                        data-confidence="<?php echo $detection->confidence_score >= 80 ? 'high' : ($detection->confidence_score >= 60 ? 'medium' : 'low'); ?>">
                        <td data-sort-value="<?php echo strtotime($detection->timestamp); ?>">
                            <?php echo date('M j, Y H:i:s', strtotime($detection->timestamp)); ?>
                        </td>
                        <td data-sort-value="<?php echo esc_attr($detection->bot_company); ?>">
                            <strong><?php echo esc_html($detection->bot_company); ?></strong>
                        </td>
                        <td data-sort-value="<?php echo esc_attr($detection->ip_address); ?>">
                            <code><?php echo esc_html($detection->ip_address); ?></code>
                        </td>
                        <td data-sort-value="<?php echo $detection->confidence_score; ?>">
                            <span class="confidence-badge confidence-<?php echo $detection->confidence_score >= 80 ? 'high' : ($detection->confidence_score >= 60 ? 'medium' : 'low'); ?>">
                                <?php echo $detection->confidence_score; ?>%
                            </span>
                        </td>
                        <td class="user-agent-cell" title="<?php echo esc_attr($detection->user_agent); ?>">
                            <?php echo esc_html(substr($detection->user_agent, 0, 50)) . (strlen($detection->user_agent) > 50 ? '...' : ''); ?>
                        </td>
                        <td class="url-cell" title="<?php echo esc_attr($detection->url); ?>">
                            <a href="<?php echo esc_url($detection->url); ?>" target="_blank" class="url-link">
                                <?php echo esc_html(substr($detection->url, 0, 30)) . (strlen($detection->url) > 30 ? '...' : ''); ?>
                            </a>
                        </td>
                        <td>
                            <span class="action-badge action-<?php echo esc_attr($detection->action_taken); ?>">
                                <?php echo esc_html(ucfirst($detection->action_taken)); ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="no-data">
            <p>No detection data available yet.</p>
            <p><em>Detections will appear here as AI bots visit your site.</em></p>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initializeTrendsChart();
    initializeCompaniesChart();
    initializeHeatmap();
    initializeTableFeatures();
    
    function initializeTrendsChart() {
        const ctx = document.getElementById('trends-chart');
        if (!ctx) return;
        
        // Get chart data via AJAX
        jQuery.post(paypercrawl_ajax.ajax_url, {
            action: 'crawlguard_get_analytics',
            nonce: paypercrawl_ajax.nonce
        }, function(response) {
            if (response.success) {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: response.data.labels,
                        datasets: [{
                            label: 'Total Detections',
                            data: response.data.detections,
                            borderColor: '#2563eb',
                            backgroundColor: 'rgba(37, 99, 235, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: 'Unique IPs',
                            data: response.data.unique_ips,
                            borderColor: '#16a34a',
                            backgroundColor: 'rgba(22, 163, 74, 0.1)',
                            tension: 0.4,
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        });
    }
    
    function initializeCompaniesChart() {
        const ctx = document.getElementById('companies-chart');
        if (!ctx) return;
        
        <?php if (!empty($stats['top_bots'])): ?>
        const companiesData = {
            labels: [<?php echo implode(',', array_map(function($bot) { return '"' . esc_js($bot->bot_company) . '"'; }, $stats['top_bots'])); ?>],
            datasets: [{
                data: [<?php echo implode(',', array_map(function($bot) { return $bot->count; }, $stats['top_bots'])); ?>],
                backgroundColor: [
                    '#2563eb',
                    '#16a34a',
                    '#dc2626',
                    '#ea580c',
                    '#7c3aed'
                ]
            }]
        };
        
        new Chart(ctx, {
            type: 'doughnut',
            data: companiesData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        <?php endif; ?>
    }
    
    function initializeHeatmap() {
        const heatmapContainer = document.getElementById('detection-heatmap');
        if (!heatmapContainer) return;
        
        // Generate 24-hour heatmap
        const hours = [];
        for (let i = 0; i < 24; i++) {
            const hour = i.toString().padStart(2, '0') + ':00';
            const intensity = Math.random(); // Replace with actual data
            hours.push(`<div class="heatmap-hour" data-hour="${i}" data-intensity="${intensity}" style="opacity: ${0.2 + (intensity * 0.8)}" title="${hour}: ${Math.floor(intensity * 100)} detections">${hour}</div>`);
        }
        heatmapContainer.innerHTML = hours.join('');
    }
    
    function initializeTableFeatures() {
        // Table filtering
        const companyFilter = document.getElementById('company-filter');
        const confidenceFilter = document.getElementById('confidence-filter');
        const tableRows = document.querySelectorAll('#detections-table tbody tr');
        
        function filterTable() {
            const companyValue = companyFilter.value.toLowerCase();
            const confidenceValue = confidenceFilter.value.toLowerCase();
            
            tableRows.forEach(row => {
                const company = row.dataset.company.toLowerCase();
                const confidence = row.dataset.confidence.toLowerCase();
                
                const companyMatch = !companyValue || company.includes(companyValue);
                const confidenceMatch = !confidenceValue || confidence === confidenceValue;
                
                row.style.display = companyMatch && confidenceMatch ? '' : 'none';
            });
        }
        
        companyFilter?.addEventListener('change', filterTable);
        confidenceFilter?.addEventListener('change', filterTable);
        
        // Table sorting
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', function() {
                const sortBy = this.dataset.sort;
                const tbody = document.querySelector('#detections-table tbody');
                const rows = Array.from(tbody.querySelectorAll('tr'));
                
                rows.sort((a, b) => {
                    const aVal = a.querySelector(`[data-sort-value]`)?.dataset.sortValue || a.cells[this.cellIndex].textContent;
                    const bVal = b.querySelector(`[data-sort-value]`)?.dataset.sortValue || b.cells[this.cellIndex].textContent;
                    
                    return isNaN(aVal) ? aVal.localeCompare(bVal) : aVal - bVal;
                });
                
                rows.forEach(row => tbody.appendChild(row));
            });
        });
    }
    
    // Chart period controls
    document.querySelectorAll('.chart-period').forEach(button => {
        button.addEventListener('click', function() {
            document.querySelectorAll('.chart-period').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Reload chart with new period (implement AJAX call)
            // For now, just visual feedback
        });
    });
});
</script>
