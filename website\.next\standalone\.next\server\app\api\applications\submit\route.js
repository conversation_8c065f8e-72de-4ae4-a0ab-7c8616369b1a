"use strict";(()=>{var e={};e.id=334,e.ids=[334],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2502:e=>{e.exports=import("prettier/plugins/html")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3505:e=>{e.exports=import("prettier/standalone")},4297:e=>{e.exports=require("async_hooks")},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{e.exports=require("crypto")},6330:e=>{e.exports=require("@prisma/client")},7075:e=>{e.exports=require("node:stream")},7207:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>h,serverHooks:()=>v,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>j});var s={};t.r(s),t.d(s,{GET:()=>x,POST:()=>m});var i=t(6559),a=t(8088),o=t(7719),n=t(2190),p=t(5069),l=t(639),u=t(4250),c=t(9991);let d=l.Ik({name:l.Yj().min(2,"Name must be at least 2 characters"),email:l.Yj().email("Invalid email address"),position:l.Yj().min(1,"Position is required"),phone:l.Yj().optional(),website:l.Yj().url().optional().or(l.eu("")),coverLetter:l.Yj().optional()});async function m(e){try{let r=await e.json(),t=d.parse(r);if(await p.db.betaApplication.findUnique({where:{email:t.email}}))return n.NextResponse.json({error:"An application with this email already exists"},{status:400});let s=await p.db.betaApplication.create({data:{name:t.name,email:t.email,position:t.position,phone:t.phone||null,website:t.website||null,coverLetter:t.coverLetter||null}});try{await (0,c.G1)(t.email,t.name)}catch(e){console.error("Failed to send confirmation email:",e)}return n.NextResponse.json({message:"Application submitted successfully",applicationId:s.id})}catch(e){if(console.error("Application submission error:",e),e instanceof u.G)return n.NextResponse.json({error:"Validation failed",details:e.issues},{status:400});return n.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(){try{let e=await p.db.betaApplication.findMany({orderBy:{createdAt:"desc"}});return n.NextResponse.json(e)}catch(e){return console.error("Error fetching applications:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let h=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/applications/submit/route",pathname:"/api/applications/submit",filename:"route",bundlePath:"app/api/applications/submit/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\applications\\submit\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:b,workUnitAsyncStorage:j,serverHooks:v}=h;function w(){return(0,o.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:j})}},7910:e=>{e.exports=require("stream")},8354:e=>{e.exports=require("util")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580,639,123],()=>t(7207));module.exports=s})();