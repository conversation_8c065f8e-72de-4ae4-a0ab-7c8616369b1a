(()=>{var e={};e.id=334,e.ids=[334],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1111:(e,t,r)=>{"use strict";r.d(t,{F7:()=>l,G1:()=>n,M:()=>d,YL:()=>p});var a=r(549),i=r(1678);let o=new a.u(process.env.RESEND_API_KEY);async function s({to:e,subject:t,html:r,from:a="PayPerCrawl <<EMAIL>>"}){try{let{data:s,error:n}=await o.emails.send({from:a,to:e,subject:t,html:r});if(await i.db.emailLog.create({data:{to:e,subject:t,body:r,status:n?"failed":"sent",provider:"resend"}}),n)throw console.error("Email send error:",n),console.error("Error details:",JSON.stringify(n,null,2)),Error(`Failed to send email: ${n.message||JSON.stringify(n)}`);return s}catch(a){throw console.error("Email service error:",a),await i.db.emailLog.create({data:{to:e,subject:t,body:r,status:"failed",provider:"resend"}}),a}}async function n(e,t){return s({to:e,subject:"Application Received - PayPerCrawl Beta",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Application Received</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2563eb;">Thank you for your application!</h1>
        
        <p>Hi ${t},</p>
        
        <p>We've received your application for the PayPerCrawl beta program. Our team will review your application and get back to you within 2-3 business days.</p>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">What's Next?</h3>
          <ul>
            <li>Our team will review your application</li>
            <li>We'll contact you if we need additional information</li>
            <li>Selected candidates will receive beta access instructions</li>
          </ul>
        </div>
        
        <p>In the meantime, feel free to explore our website and learn more about how PayPerCrawl can help monetize your AI content.</p>
        
        <p>Best regards,<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function l(e,t,r){return s({to:e,subject:"Welcome to PayPerCrawl Waitlist!",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Waitlist Confirmation</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2563eb;">You're on the waitlist! 🎉</h1>
        
        <p>Hi ${t},</p>
        
        <p>Thank you for joining the PayPerCrawl waitlist! You're currently <strong>#${r}</strong> in line.</p>
        
        <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb;">
          <h3 style="margin-top: 0; color: #2563eb;">Your Position: #${r}</h3>
          <p style="margin-bottom: 0;">We'll notify you as soon as beta access becomes available!</p>
        </div>
        
        <p>While you wait, here's what you can do:</p>
        <ul>
          <li>Follow us on social media for updates</li>
          <li>Share PayPerCrawl with friends who create AI content</li>
          <li>Read our blog for AI monetization insights</li>
        </ul>
        
        <p>We're working hard to launch the beta and can't wait to help you monetize your AI content!</p>
        
        <p>Best regards,<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function p(e,t,r){let a=`https://paypercrawl.tech/?invited=true&token=${r}`;return s({to:e,subject:"Your PayPerCrawl Beta Access is Ready! \uD83D\uDE80",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Beta Access Ready</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #16a34a;">Welcome to PayPerCrawl Beta! 🚀</h1>
        
        <p>Hi ${t},</p>
        
        <p>Congratulations! You've been selected for the PayPerCrawl beta program. You can now start monetizing your AI content!</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${a}" style="background: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
            Access Beta Dashboard
          </a>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #16a34a;">
          <h3 style="margin-top: 0; color: #16a34a;">Getting Started</h3>
          <ol>
            <li>Click the button above to access your beta dashboard</li>
            <li>Install the PayPerCrawl WordPress plugin</li>
            <li>Configure your monetization settings</li>
            <li>Start earning from AI bot traffic!</li>
          </ol>
        </div>
        
        <p>Need help? Our support team is standing by to assist you with setup and configuration.</p>
        
        <p>Welcome aboard!<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function d(e,t,r,a){let i=`New Contact Form Submission: ${r}`,o=`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Contact Form Submission</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #dc2626;">New Contact Form Submission</h1>
        
        <div style="background: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Name:</strong> ${e}</p>
          <p><strong>Email:</strong> ${t}</p>
          <p><strong>Subject:</strong> ${r}</p>
          <p><strong>Message:</strong></p>
          <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
            ${a.replace(/\n/g,"<br>")}
          </div>
        </div>
        
        <p>Please respond to this inquiry promptly.</p>
      </div>
    </body>
    </html>
  `;return s({to:process.env.ADMIN_EMAIL||"<EMAIL>",subject:i,html:o})}},1678:(e,t,r)=>{"use strict";r.d(t,{db:()=>i});let a=require("@prisma/client"),i=globalThis.prisma??new a.PrismaClient({log:["query"]})},2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3505:e=>{"use strict";e.exports=import("prettier/standalone")},4297:e=>{"use strict";e.exports=require("async_hooks")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},7075:e=>{"use strict";e.exports=require("node:stream")},7207:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>y,serverHooks:()=>f,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{GET:()=>h,POST:()=>m});var i=r(6559),o=r(8088),s=r(7719),n=r(2190),l=r(1678),p=r(639),d=r(4250),c=r(1111);let u=p.Ik({name:p.Yj().min(2,"Name must be at least 2 characters"),email:p.Yj().email("Invalid email address"),position:p.Yj().min(1,"Position is required"),phone:p.Yj().optional(),website:p.Yj().url().optional().or(p.eu("")),coverLetter:p.Yj().optional()});async function m(e){try{let t=await e.json(),r=u.parse(t);if(await l.db.betaApplication.findUnique({where:{email:r.email}}))return n.NextResponse.json({error:"An application with this email already exists"},{status:400});let a=await l.db.betaApplication.create({data:{name:r.name,email:r.email,position:r.position,phone:r.phone||null,website:r.website||null,coverLetter:r.coverLetter||null}});try{await (0,c.G1)(r.email,r.name)}catch(e){console.error("Failed to send confirmation email:",e)}return n.NextResponse.json({message:"Application submitted successfully",applicationId:a.id})}catch(e){if(console.error("Application submission error:",e),e instanceof d.G)return n.NextResponse.json({error:"Validation failed",details:e.issues},{status:400});return n.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(){try{let e=await l.db.betaApplication.findMany({orderBy:{createdAt:"desc"}});return n.NextResponse.json(e)}catch(e){return console.error("Error fetching applications:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let y=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/applications/submit/route",pathname:"/api/applications/submit",filename:"route",bundlePath:"app/api/applications/submit/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\applications\\submit\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:b,workUnitAsyncStorage:g,serverHooks:f}=y;function w(){return(0,s.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:g})}},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,580,987],()=>r(7207));module.exports=a})();