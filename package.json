{"name": "crawlguard-wp", "version": "1.0.0", "description": "WordPress plugin for AI content monetization - The Stripe for AI Content Access", "main": "crawlguard-wp.php", "scripts": {"dev": "webpack --mode development --watch", "build": "webpack --mode production", "lint": "npm run lint:js && npm run lint:php", "lint:js": "eslint assets/js/**/*.js", "lint:php": "phpcs --standard=WordPress .", "format": "prettier --write assets/js/**/*.js", "format:check": "prettier --check assets/js/**/*.js", "test": "phpunit", "test:js": "jest", "test:watch": "jest --watch", "zip": "npm run build && zip -r crawlguard-wp.zip . -x node_modules/\\* .git/\\* *.zip .env*", "release": "npm run build && npm run test && npm run zip", "start": "npm run dev"}, "keywords": ["wordpress", "plugin", "ai", "monetization", "bot-detection", "content-protection", "revenue"], "author": "CrawlGuard Team", "license": "GPL-2.0-or-later", "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "babel-loader": "^9.1.0", "css-loader": "^6.8.0", "eslint": "^8.42.0", "eslint-config-wordpress": "^2.0.0", "jest": "^29.5.0", "mini-css-extract-plugin": "^2.7.0", "prettier": "^2.8.8", "webpack": "^5.88.0", "webpack-cli": "^5.1.0"}, "dependencies": {"axios": "^1.4.0", "chart.js": "^4.3.0", "itty-router": "^4.2.2", "react": "^18.2.0", "react-dom": "^18.2.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/crawlguard-wp.git"}, "bugs": {"url": "https://github.com/yourusername/crawlguard-wp/issues"}, "homepage": "https://crawlguard.com"}