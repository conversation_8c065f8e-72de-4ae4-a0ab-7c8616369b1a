"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[912],{1362:(e,t,r)=>{r.d(t,{D:()=>c,N:()=>d});var o=r(2115),n=(e,t,r,o,n,l,a,s)=>{let i=document.documentElement,c=["light","dark"];function d(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,o=r&&l?n.map(e=>l[e]||e):n;r?(i.classList.remove(...o),i.classList.add(l&&l[t]?l[t]:t)):i.setAttribute(e,t)}),r=t,s&&c.includes(r)&&(i.style.colorScheme=r)}if(o)d(o);else try{let e=localStorage.getItem(t)||r,o=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(o)}catch(e){}},l=["light","dark"],a="(prefers-color-scheme: dark)",s=o.createContext(void 0),i={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=o.useContext(s))?e:i},d=e=>o.useContext(s)?o.createElement(o.Fragment,null,e.children):o.createElement(m,{...e}),u=["light","dark"],m=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:n=!0,enableColorScheme:i=!0,storageKey:c="theme",themes:d=u,defaultTheme:m=n?"system":"light",attribute:h="data-theme",value:v,children:y,nonce:w,scriptProps:k}=e,[x,E]=o.useState(()=>f(c,m)),[N,C]=o.useState(()=>"system"===x?g():x),z=v?Object.values(v):d,S=o.useCallback(e=>{let t=e;if(!t)return;"system"===e&&n&&(t=g());let o=v?v[t]:t,a=r?b(w):null,s=document.documentElement,c=e=>{"class"===e?(s.classList.remove(...z),o&&s.classList.add(o)):e.startsWith("data-")&&(o?s.setAttribute(e,o):s.removeAttribute(e))};if(Array.isArray(h)?h.forEach(c):c(h),i){let e=l.includes(m)?m:null,r=l.includes(t)?t:e;s.style.colorScheme=r}null==a||a()},[w]),T=o.useCallback(e=>{let t="function"==typeof e?e(x):e;E(t);try{localStorage.setItem(c,t)}catch(e){}},[x]),M=o.useCallback(e=>{C(g(e)),"system"===x&&n&&!t&&S("system")},[x,t]);o.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(M),M(e),()=>e.removeListener(M)},[M]),o.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?E(e.newValue):T(m))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[T]),o.useEffect(()=>{S(null!=t?t:x)},[t,x]);let O=o.useMemo(()=>({theme:x,setTheme:T,forcedTheme:t,resolvedTheme:"system"===x?N:x,themes:n?[...d,"system"]:d,systemTheme:n?N:void 0}),[x,T,t,N,n,d]);return o.createElement(s.Provider,{value:O},o.createElement(p,{forcedTheme:t,storageKey:c,attribute:h,enableSystem:n,enableColorScheme:i,defaultTheme:m,value:v,themes:d,nonce:w,scriptProps:k}),y)},p=o.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:l,enableSystem:a,enableColorScheme:s,defaultTheme:i,value:c,themes:d,nonce:u,scriptProps:m}=e,p=JSON.stringify([l,r,i,t,d,c,a,s]).slice(1,-1);return o.createElement("script",{...m,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(n.toString(),")(").concat(p,")")}})}),f=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},b=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")},2085:(e,t,r)=>{r.d(t,{F:()=>a});var o=r(2596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=o.$,a=(e,t)=>r=>{var o;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:s}=t,i=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],o=null==s?void 0:s[e];if(null===t)return null;let l=n(t)||n(o);return a[e][l]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return l(e,i,null==t||null==(o=t.compoundVariants)?void 0:o.reduce((e,t)=>{let{class:r,className:o,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...c}[t]):({...s,...c})[t]===r})?[...e,r,o]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2596:(e,t,r)=>{r.d(t,{$:()=>o});function o(){for(var e,t,r=0,o="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,o,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var l=t.length;for(r=0;r<l;r++)t[r]&&(o=e(t[r]))&&(n&&(n+=" "),n+=o)}else for(o in t)t[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=t);return o}},2712:(e,t,r)=>{r.d(t,{N:()=>n});var o=r(2115),n=globalThis?.document?o.useLayoutEffect:()=>{}},3655:(e,t,r)=>{r.d(t,{hO:()=>i,sG:()=>s});var o=r(2115),n=r(7650),l=r(9708),a=r(5155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,l.TL)(`Primitive.${t}`),n=o.forwardRef((e,o)=>{let{asChild:n,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n?r:t,{...l,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function i(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}},4378:(e,t,r)=>{r.d(t,{Z:()=>i});var o=r(2115),n=r(7650),l=r(3655),a=r(2712),s=r(5155),i=o.forwardRef((e,t)=>{var r,i;let{container:c,...d}=e,[u,m]=o.useState(!1);(0,a.N)(()=>m(!0),[]);let p=c||u&&(null==(i=globalThis)||null==(r=i.document)?void 0:r.body);return p?n.createPortal((0,s.jsx)(l.sG.div,{...d,ref:t}),p):null});i.displayName="Portal"},5185:(e,t,r)=>{r.d(t,{m:()=>o});function o(e,t,{checkForDefaultPrevented:r=!0}={}){return function(o){if(e?.(o),!1===r||!o.defaultPrevented)return t?.(o)}}},5845:(e,t,r)=>{r.d(t,{i:()=>s});var o,n=r(2115),l=r(2712),a=(o||(o=r.t(n,2)))[" useInsertionEffect ".trim().toString()]||l.N;function s({prop:e,defaultProp:t,onChange:r=()=>{},caller:o}){let[l,s,i]=function({defaultProp:e,onChange:t}){let[r,o]=n.useState(e),l=n.useRef(r),s=n.useRef(t);return a(()=>{s.current=t},[t]),n.useEffect(()=>{l.current!==r&&(s.current?.(r),l.current=r)},[r,l]),[r,o,s]}({defaultProp:t,onChange:r}),c=void 0!==e,d=c?e:l;{let t=n.useRef(void 0!==e);n.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${o} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,o])}return[d,n.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&i.current?.(r)}else s(t)},[c,e,s,i])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,r)=>{r.d(t,{A:()=>l});var o=r(2115),n=r(5155);function l(e,t=[]){let r=[],a=()=>{let t=r.map(e=>o.createContext(e));return function(r){let n=r?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return a.scopeName=e,[function(t,l){let a=o.createContext(l),s=r.length;r=[...r,l];let i=t=>{let{scope:r,children:l,...i}=t,c=r?.[e]?.[s]||a,d=o.useMemo(()=>i,Object.values(i));return(0,n.jsx)(c.Provider,{value:d,children:l})};return i.displayName=t+"Provider",[i,function(r,n){let i=n?.[e]?.[s]||a,c=o.useContext(i);if(c)return c;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:o})=>{let n=r(e)[`__scope${o}`];return{...t,...n}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(a,...t)]}},6101:(e,t,r)=>{r.d(t,{s:()=>a,t:()=>l});var o=r(2115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,o=e.map(e=>{let o=n(e,t);return r||"function"!=typeof o||(r=!0),o});if(r)return()=>{for(let t=0;t<o.length;t++){let r=o[t];"function"==typeof r?r():n(e[t],null)}}}}function a(...e){return o.useCallback(l(...e),e)}},7328:(e,t,r)=>{function o(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function n(e,t){var r=o(e,t,"get");return r.get?r.get.call(e):r.value}function l(e,t,r){var n=o(e,t,"set");if(n.set)n.set.call(e,r);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=r}return r}r.d(t,{N:()=>m});var a,s=r(2115),i=r(6081),c=r(6101),d=r(9708),u=r(5155);function m(e){let t=e+"CollectionProvider",[r,o]=(0,i.A)(t),[n,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:r}=e,o=s.useRef(null),l=s.useRef(new Map).current;return(0,u.jsx)(n,{scope:t,itemMap:l,collectionRef:o,children:r})};a.displayName=t;let m=e+"CollectionSlot",p=(0,d.TL)(m),f=s.forwardRef((e,t)=>{let{scope:r,children:o}=e,n=l(m,r),a=(0,c.s)(t,n.collectionRef);return(0,u.jsx)(p,{ref:a,children:o})});f.displayName=m;let b=e+"CollectionItemSlot",g="data-radix-collection-item",h=(0,d.TL)(b),v=s.forwardRef((e,t)=>{let{scope:r,children:o,...n}=e,a=s.useRef(null),i=(0,c.s)(t,a),d=l(b,r);return s.useEffect(()=>(d.itemMap.set(a,{ref:a,...n}),()=>void d.itemMap.delete(a))),(0,u.jsx)(h,{...{[g]:""},ref:i,children:o})});return v.displayName=b,[{Provider:a,Slot:f,ItemSlot:v},function(t){let r=l(e+"CollectionConsumer",t);return s.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(g,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},o]}var p=new WeakMap;function f(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,o=b(t),n=o>=0?o:r+o;return n<0||n>=r?-1:n}(e,t);return -1===r?void 0:e[r]}function b(e){return e!=e||0===e?0:Math.trunc(e)}a=new WeakMap},8905:(e,t,r)=>{r.d(t,{C:()=>a});var o=r(2115),n=r(6101),l=r(2712),a=e=>{let{present:t,children:r}=e,a=function(e){var t,r;let[n,a]=o.useState(),i=o.useRef(null),c=o.useRef(e),d=o.useRef("none"),[u,m]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let o=r[e][t];return null!=o?o:e},t));return o.useEffect(()=>{let e=s(i.current);d.current="mounted"===u?e:"none"},[u]),(0,l.N)(()=>{let t=i.current,r=c.current;if(r!==e){let o=d.current,n=s(t);e?m("MOUNT"):"none"===n||(null==t?void 0:t.display)==="none"?m("UNMOUNT"):r&&o!==n?m("ANIMATION_OUT"):m("UNMOUNT"),c.current=e}},[e,m]),(0,l.N)(()=>{if(n){var e;let t,r=null!=(e=n.ownerDocument.defaultView)?e:window,o=e=>{let o=s(i.current).includes(e.animationName);if(e.target===n&&o&&(m("ANIMATION_END"),!c.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},l=e=>{e.target===n&&(d.current=s(i.current))};return n.addEventListener("animationstart",l),n.addEventListener("animationcancel",o),n.addEventListener("animationend",o),()=>{r.clearTimeout(t),n.removeEventListener("animationstart",l),n.removeEventListener("animationcancel",o),n.removeEventListener("animationend",o)}}m("ANIMATION_END")},[n,m]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:o.useCallback(e=>{i.current=e?getComputedStyle(e):null,a(e)},[])}}(t),i="function"==typeof r?r({present:a.isPresent}):o.Children.only(r),c=(0,n.s)(a.ref,function(e){var t,r;let o=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,n=o&&"isReactWarning"in o&&o.isReactWarning;return n?e.ref:(n=(o=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in o&&o.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||a.isPresent?o.cloneElement(i,{ref:c}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},9033:(e,t,r)=>{r.d(t,{c:()=>n});var o=r(2115);function n(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,r)=>{r.d(t,{lg:()=>h,qW:()=>m,bL:()=>g});var o,n=r(2115),l=r(5185),a=r(3655),s=r(6101),i=r(9033),c=r(5155),d="dismissableLayer.update",u=n.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),m=n.forwardRef((e,t)=>{var r,m;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:v,onInteractOutside:y,onDismiss:w,...k}=e,x=n.useContext(u),[E,N]=n.useState(null),C=null!=(m=null==E?void 0:E.ownerDocument)?m:null==(r=globalThis)?void 0:r.document,[,z]=n.useState({}),S=(0,s.s)(t,e=>N(e)),T=Array.from(x.layers),[M]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),O=T.indexOf(M),P=E?T.indexOf(E):-1,L=x.layersWithOutsidePointerEventsDisabled.size>0,A=P>=O,j=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,o=(0,i.c)(e),l=n.useRef(!1),a=n.useRef(()=>{});return n.useEffect(()=>{let e=e=>{if(e.target&&!l.current){let t=function(){b("dismissableLayer.pointerDownOutside",o,n,{discrete:!0})},n={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",a.current),a.current=t,r.addEventListener("click",a.current,{once:!0})):t()}else r.removeEventListener("click",a.current);l.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",a.current)}},[r,o]),{onPointerDownCapture:()=>l.current=!0}}(e=>{let t=e.target,r=[...x.branches].some(e=>e.contains(t));A&&!r&&(null==h||h(e),null==y||y(e),e.defaultPrevented||null==w||w())},C),R=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,o=(0,i.c)(e),l=n.useRef(!1);return n.useEffect(()=>{let e=e=>{e.target&&!l.current&&b("dismissableLayer.focusOutside",o,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,o]),{onFocusCapture:()=>l.current=!0,onBlurCapture:()=>l.current=!1}}(e=>{let t=e.target;![...x.branches].some(e=>e.contains(t))&&(null==v||v(e),null==y||y(e),e.defaultPrevented||null==w||w())},C);return!function(e,t=globalThis?.document){let r=(0,i.c)(e);n.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{P===x.layers.size-1&&(null==g||g(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},C),n.useEffect(()=>{if(E)return p&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(o=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(E)),x.layers.add(E),f(),()=>{p&&1===x.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=o)}},[E,C,p,x]),n.useEffect(()=>()=>{E&&(x.layers.delete(E),x.layersWithOutsidePointerEventsDisabled.delete(E),f())},[E,x]),n.useEffect(()=>{let e=()=>z({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,c.jsx)(a.sG.div,{...k,ref:S,style:{pointerEvents:L?A?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.m)(e.onFocusCapture,R.onFocusCapture),onBlurCapture:(0,l.m)(e.onBlurCapture,R.onBlurCapture),onPointerDownCapture:(0,l.m)(e.onPointerDownCapture,j.onPointerDownCapture)})});m.displayName="DismissableLayer";var p=n.forwardRef((e,t)=>{let r=n.useContext(u),o=n.useRef(null),l=(0,s.s)(t,o);return n.useEffect(()=>{let e=o.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,c.jsx)(a.sG.div,{...e,ref:l})});function f(){let e=new CustomEvent(d);document.dispatchEvent(e)}function b(e,t,r,o){let{discrete:n}=o,l=r.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&l.addEventListener(e,t,{once:!0}),n?(0,a.hO)(l,s):l.dispatchEvent(s)}p.displayName="DismissableLayerBranch";var g=m,h=p},9688:(e,t,r)=>{r.d(t,{QP:()=>ec});let o=e=>{let t=s(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&o[e]?[...n,...o[e]]:n}}},n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],o=t.nextPart.get(r),l=o?n(e.slice(1),o):void 0;if(l)return l;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},l=/^\[(.+)\]$/,a=e=>{if(l.test(e)){let t=l.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},s=e=>{let{theme:t,classGroups:r}=e,o={nextPart:new Map,validators:[]};for(let e in r)i(r[e],o,e,t);return o},i=(e,t,r,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:c(t,e)).classGroupId=r;return}if("function"==typeof e)return d(e)?void i(e(o),t,r,o):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,n])=>{i(n,c(t,e),r,o)})})},c=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},d=e=>e.isThemeGetter,u=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,o=new Map,n=(n,l)=>{r.set(n,l),++t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}},m=e=>{let{prefix:t,experimentalParseClassName:r}=e,o=e=>{let t,r=[],o=0,n=0,l=0;for(let a=0;a<e.length;a++){let s=e[a];if(0===o&&0===n){if(":"===s){r.push(e.slice(l,a)),l=a+1;continue}if("/"===s){t=a;continue}}"["===s?o++:"]"===s?o--:"("===s?n++:")"===s&&n--}let a=0===r.length?e:e.substring(l),s=p(a);return{modifiers:r,hasImportantModifier:s!==a,baseClassName:s,maybePostfixModifierPosition:t&&t>l?t-l:void 0}};if(t){let e=t+":",r=o;o=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=o;o=t=>r({className:t,parseClassName:e})}return o},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,f=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],o=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...o.sort(),e),o=[]):o.push(e)}),r.push(...o.sort()),r}},b=e=>({cache:u(e.cacheSize),parseClassName:m(e),sortModifiers:f(e),...o(e)}),g=/\s+/,h=(e,t)=>{let{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:l}=t,a=[],s=e.trim().split(g),i="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{isExternal:c,modifiers:d,hasImportantModifier:u,baseClassName:m,maybePostfixModifierPosition:p}=r(t);if(c){i=t+(i.length>0?" "+i:i);continue}let f=!!p,b=o(f?m.substring(0,p):m);if(!b){if(!f||!(b=o(m))){i=t+(i.length>0?" "+i:i);continue}f=!1}let g=l(d).join(":"),h=u?g+"!":g,v=h+b;if(a.includes(v))continue;a.push(v);let y=n(b,f);for(let e=0;e<y.length;++e){let t=y[e];a.push(h+t)}i=t+(i.length>0?" "+i:i)}return i};function v(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=y(e))&&(o&&(o+=" "),o+=t);return o}let y=e=>{let t;if("string"==typeof e)return e;let r="";for(let o=0;o<e.length;o++)e[o]&&(t=y(e[o]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},k=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,x=/^\((?:(\w[\w-]*):)?(.+)\)$/i,E=/^\d+\/\d+$/,N=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,C=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,z=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,S=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,T=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,M=e=>E.test(e),O=e=>!!e&&!Number.isNaN(Number(e)),P=e=>!!e&&Number.isInteger(Number(e)),L=e=>e.endsWith("%")&&O(e.slice(0,-1)),A=e=>N.test(e),j=()=>!0,R=e=>C.test(e)&&!z.test(e),D=()=>!1,I=e=>S.test(e),W=e=>T.test(e),$=e=>!G(e)&&!Z(e),_=e=>ee(e,en,D),G=e=>k.test(e),U=e=>ee(e,el,R),F=e=>ee(e,ea,O),V=e=>ee(e,er,D),B=e=>ee(e,eo,W),q=e=>ee(e,ei,I),Z=e=>x.test(e),K=e=>et(e,el),H=e=>et(e,es),X=e=>et(e,er),J=e=>et(e,en),Q=e=>et(e,eo),Y=e=>et(e,ei,!0),ee=(e,t,r)=>{let o=k.exec(e);return!!o&&(o[1]?t(o[1]):r(o[2]))},et=(e,t,r=!1)=>{let o=x.exec(e);return!!o&&(o[1]?t(o[1]):r)},er=e=>"position"===e||"percentage"===e,eo=e=>"image"===e||"url"===e,en=e=>"length"===e||"size"===e||"bg-size"===e,el=e=>"length"===e,ea=e=>"number"===e,es=e=>"family-name"===e,ei=e=>"shadow"===e;Symbol.toStringTag;let ec=function(e,...t){let r,o,n,l=function(s){return o=(r=b(t.reduce((e,t)=>t(e),e()))).cache.get,n=r.cache.set,l=a,a(s)};function a(e){let t=o(e);if(t)return t;let l=h(e,r);return n(e,l),l}return function(){return l(v.apply(null,arguments))}}(()=>{let e=w("color"),t=w("font"),r=w("text"),o=w("font-weight"),n=w("tracking"),l=w("leading"),a=w("breakpoint"),s=w("container"),i=w("spacing"),c=w("radius"),d=w("shadow"),u=w("inset-shadow"),m=w("text-shadow"),p=w("drop-shadow"),f=w("blur"),b=w("perspective"),g=w("aspect"),h=w("ease"),v=w("animate"),y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],k=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],x=()=>[...k(),Z,G],E=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto","contain","none"],C=()=>[Z,G,i],z=()=>[M,"full","auto",...C()],S=()=>[P,"none","subgrid",Z,G],T=()=>["auto",{span:["full",P,Z,G]},P,Z,G],R=()=>[P,"auto",Z,G],D=()=>["auto","min","max","fr",Z,G],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],W=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...C()],et=()=>[M,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...C()],er=()=>[e,Z,G],eo=()=>[...k(),X,V,{position:[Z,G]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],el=()=>["auto","cover","contain",J,_,{size:[Z,G]}],ea=()=>[L,K,U],es=()=>["","none","full",c,Z,G],ei=()=>["",O,K,U],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[O,L,X,V],em=()=>["","none",f,Z,G],ep=()=>["none",O,Z,G],ef=()=>["none",O,Z,G],eb=()=>[O,Z,G],eg=()=>[M,"full",...C()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[A],breakpoint:[A],color:[j],container:[A],"drop-shadow":[A],ease:["in","out","in-out"],font:[$],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[A],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[A],shadow:[A],spacing:["px",O],text:[A],"text-shadow":[A],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",M,G,Z,g]}],container:["container"],columns:[{columns:[O,G,Z,s]}],"break-after":[{"break-after":y()}],"break-before":[{"break-before":y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:x()}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:z()}],"inset-x":[{"inset-x":z()}],"inset-y":[{"inset-y":z()}],start:[{start:z()}],end:[{end:z()}],top:[{top:z()}],right:[{right:z()}],bottom:[{bottom:z()}],left:[{left:z()}],visibility:["visible","invisible","collapse"],z:[{z:[P,"auto",Z,G]}],basis:[{basis:[M,"full","auto",s,...C()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[O,M,"auto","initial","none",G]}],grow:[{grow:["",O,Z,G]}],shrink:[{shrink:["",O,Z,G]}],order:[{order:[P,"first","last","none",Z,G]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:T()}],"col-start":[{"col-start":R()}],"col-end":[{"col-end":R()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:T()}],"row-start":[{"row-start":R()}],"row-end":[{"row-end":R()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":D()}],"auto-rows":[{"auto-rows":D()}],gap:[{gap:C()}],"gap-x":[{"gap-x":C()}],"gap-y":[{"gap-y":C()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[...W(),"normal"]}],"justify-self":[{"justify-self":["auto",...W()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[...W(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...W(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[...W(),"baseline"]}],"place-self":[{"place-self":["auto",...W()]}],p:[{p:C()}],px:[{px:C()}],py:[{py:C()}],ps:[{ps:C()}],pe:[{pe:C()}],pt:[{pt:C()}],pr:[{pr:C()}],pb:[{pb:C()}],pl:[{pl:C()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":C()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":C()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[s,"screen",...et()]}],"min-w":[{"min-w":[s,"screen","none",...et()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[a]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,K,U]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,Z,F]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",L,G]}],"font-family":[{font:[H,G,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,Z,G]}],"line-clamp":[{"line-clamp":[O,"none",Z,F]}],leading:[{leading:[l,...C()]}],"list-image":[{"list-image":["none",Z,G]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Z,G]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[O,"from-font","auto",Z,U]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[O,"auto",Z,G]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Z,G]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Z,G]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:el()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},P,Z,G],radial:["",Z,G],conic:[P,Z,G]},Q,B]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[O,Z,G]}],"outline-w":[{outline:["",O,K,U]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",d,Y,q]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",u,Y,q]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[O,U]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",m,Y,q]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[O,Z,G]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[O]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[Z,G]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":k()}],"mask-image-conic-pos":[{"mask-conic":[O]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:el()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Z,G]}],filter:[{filter:["","none",Z,G]}],blur:[{blur:em()}],brightness:[{brightness:[O,Z,G]}],contrast:[{contrast:[O,Z,G]}],"drop-shadow":[{"drop-shadow":["","none",p,Y,q]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",O,Z,G]}],"hue-rotate":[{"hue-rotate":[O,Z,G]}],invert:[{invert:["",O,Z,G]}],saturate:[{saturate:[O,Z,G]}],sepia:[{sepia:["",O,Z,G]}],"backdrop-filter":[{"backdrop-filter":["","none",Z,G]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[O,Z,G]}],"backdrop-contrast":[{"backdrop-contrast":[O,Z,G]}],"backdrop-grayscale":[{"backdrop-grayscale":["",O,Z,G]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[O,Z,G]}],"backdrop-invert":[{"backdrop-invert":["",O,Z,G]}],"backdrop-opacity":[{"backdrop-opacity":[O,Z,G]}],"backdrop-saturate":[{"backdrop-saturate":[O,Z,G]}],"backdrop-sepia":[{"backdrop-sepia":["",O,Z,G]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":C()}],"border-spacing-x":[{"border-spacing-x":C()}],"border-spacing-y":[{"border-spacing-y":C()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Z,G]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[O,"initial",Z,G]}],ease:[{ease:["linear","initial",h,Z,G]}],delay:[{delay:[O,Z,G]}],animate:[{animate:["none",v,Z,G]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,Z,G]}],"perspective-origin":[{"perspective-origin":x()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[Z,G,"","none","gpu","cpu"]}],"transform-origin":[{origin:x()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Z,G]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Z,G]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[O,K,U,F]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,t,r)=>{r.d(t,{DX:()=>s,TL:()=>a});var o=r(2115),n=r(6101),l=r(5155);function a(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:r,...l}=e;if(o.isValidElement(r)){var a;let e,s,i=(a=r,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let r={...t};for(let o in t){let n=e[o],l=t[o];/^on[A-Z]/.test(o)?n&&l?r[o]=(...e)=>{let t=l(...e);return n(...e),t}:n&&(r[o]=n):"style"===o?r[o]={...n,...l}:"className"===o&&(r[o]=[n,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==o.Fragment&&(c.ref=t?(0,n.t)(t,i):i),o.cloneElement(r,c)}return o.Children.count(r)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=o.forwardRef((e,r)=>{let{children:n,...a}=e,s=o.Children.toArray(n),i=s.find(c);if(i){let e=i.props.children,n=s.map(t=>t!==i?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...a,ref:r,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,l.jsx)(t,{...a,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var s=a("Slot"),i=Symbol("radix.slottable");function c(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},9946:(e,t,r)=>{r.d(t,{A:()=>u});var o=r(2115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),a=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,o.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:a,className:d="",children:u,iconNode:m,...p}=e;return(0,o.createElement)("svg",{ref:t,...c,width:n,height:n,stroke:r,strokeWidth:a?24*Number(l)/Number(n):l,className:s("lucide",d),...!u&&!i(p)&&{"aria-hidden":"true"},...p},[...m.map(e=>{let[t,r]=e;return(0,o.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,o.forwardRef)((r,l)=>{let{className:i,...c}=r;return(0,o.createElement)(d,{ref:l,iconNode:t,className:s("lucide-".concat(n(a(e))),"lucide-".concat(e),i),...c})});return r.displayName=a(e),r}}}]);