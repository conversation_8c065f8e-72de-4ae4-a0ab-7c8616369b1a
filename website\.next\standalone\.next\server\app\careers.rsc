1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[639,["455","static/chunks/455-3ab14db91ded209f.js","631","static/chunks/631-000f2cc4aa5fbe34.js","177","static/chunks/app/layout-4b04e726a6383e6b.js"],"Toaster"]
5:I[894,[],"ClientPageRoot"]
6:I[2226,["455","static/chunks/455-3ab14db91ded209f.js","38","static/chunks/38-506e88a59d3c3b17.js","846","static/chunks/app/careers/page-bb1f731ccc667dad.js"],"default"]
9:I[9665,[],"OutletBoundary"]
c:I[4911,[],"AsyncMetadataOutlet"]
e:I[9665,[],"ViewportBoundary"]
10:I[9665,[],"MetadataBoundary"]
12:I[6614,[],""]
:HL["/_next/static/css/f30152c0704fba31.css","style"]
:HL["/_next/static/css/a095e833d56c955d.css","style"]
0:{"P":null,"b":"KJctX2vIK1dvgs0FQrCsY","p":"","c":["","careers",""],"i":false,"f":[[["",{"children":["careers",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/f30152c0704fba31.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/a095e833d56c955d.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased bg-background text-foreground","children":[["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}],["$","$L4",null,{}]]}]}]]}],{"children":["careers",["$","$1","c",{"children":[null,["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L5",null,{"Component":"$6","searchParams":{},"params":{},"promises":["$@7","$@8"]}],null,["$","$L9",null,{"children":["$La","$Lb",["$","$Lc",null,{"promise":"$@d"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","2O7g1-JnbRI-OgAsrnnwLv",{"children":[["$","$Le",null,{"children":"$Lf"}],null]}],["$","$L10",null,{"children":"$L11"}]]}],false]],"m":"$undefined","G":["$12","$undefined"],"s":false,"S":true}
13:"$Sreact.suspense"
14:I[4911,[],"AsyncMetadata"]
7:{}
8:{}
11:["$","div",null,{"hidden":true,"children":["$","$13",null,{"fallback":null,"children":["$","$L14",null,{"promise":"$@15"}]}]}]
b:null
f:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
a:null
d:{"metadata":[["$","title","0",{"children":"PayPerCrawl - AI Content Monetization Platform"}],["$","meta","1",{"name":"description","content":"Turn AI bot traffic into revenue. PayPerCrawl detects AI bots crawling your content and converts them into paying customers. Built for WordPress with enterprise-grade security."}],["$","meta","2",{"name":"author","content":"PayPerCrawl Team"}],["$","meta","3",{"name":"keywords","content":"AI monetization,WordPress,content protection,bot detection,revenue generation,AI training data"}],["$","meta","4",{"property":"og:title","content":"PayPerCrawl - AI Content Monetization Platform"}],["$","meta","5",{"property":"og:description","content":"Turn AI bot traffic into revenue with advanced bot detection and monetization"}],["$","meta","6",{"property":"og:url","content":"https://paypercrawl.tech/"}],["$","meta","7",{"property":"og:site_name","content":"PayPerCrawl"}],["$","meta","8",{"property":"og:type","content":"website"}],["$","meta","9",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","10",{"name":"twitter:title","content":"PayPerCrawl - AI Content Monetization Platform"}],["$","meta","11",{"name":"twitter:description","content":"Turn AI bot traffic into revenue with advanced bot detection and monetization"}],["$","link","12",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"128x128"}]],"error":null,"digest":"$undefined"}
15:{"metadata":"$d:metadata","error":null,"digest":"$undefined"}
