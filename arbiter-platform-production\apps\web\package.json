{"name": "@arbiter/web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "13.4.12", "react": "18.2.0", "react-dom": "18.2.0", "@tanstack/react-query": "^4.29.19", "axios": "^1.4.0", "react-hook-form": "^7.45.2", "@hookform/resolvers": "^3.1.1", "zod": "^3.21.4", "zustand": "^4.3.9", "framer-motion": "^10.12.18", "lucide-react": "^0.263.1", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "class-variance-authority": "^0.7.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-toast": "^1.1.4", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-progress": "^1.0.3", "react-dropzone": "^14.2.3", "recharts": "^2.7.2", "date-fns": "^2.30.0"}, "devDependencies": {"@types/node": "20.4.2", "@types/react": "18.2.15", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.14", "eslint": "8.45.0", "eslint-config-next": "13.4.12", "postcss": "8.4.27", "tailwindcss": "3.3.3", "typescript": "5.1.6"}}