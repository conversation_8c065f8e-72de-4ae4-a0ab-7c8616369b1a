# Database
DATABASE_URL="postgresql://username:password@localhost:5432/arbiter_platform_dev"

# Authentication
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
BCRYPT_ROUNDS=12

# OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Email
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
FROM_EMAIL="<EMAIL>"

# File Storage
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="arbiter-platform-uploads"
AWS_CLOUDFRONT_DOMAIN="your-cloudfront-domain.com"

# Redis
REDIS_URL="redis://localhost:6379"

# Stripe
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key"
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
STRIPE_WEBHOOK_SECRET="whsec_your-stripe-webhook-secret"

# Application
NODE_ENV="development"
APP_URL="http://localhost:3000"
API_URL="http://localhost:4000"
PORT=3000
API_PORT=4000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10737418240  # 10GB in bytes
ALLOWED_FILE_TYPES="image/*,video/*,audio/*,text/*,application/pdf,application/json"

# Security
CORS_ORIGINS="http://localhost:3000,http://localhost:3001"
COOKIE_SECRET="your-cookie-secret-change-this"
SESSION_SECRET="your-session-secret-change-this"

# Monitoring
SENTRY_DSN="https://<EMAIL>/project-id"
DATADOG_API_KEY="your-datadog-api-key"

# Search
ELASTICSEARCH_URL="http://localhost:9200"
ELASTICSEARCH_INDEX="arbiter_platform"

# AI Services
OPENAI_API_KEY="your-openai-api-key"
ANTHROPIC_API_KEY="your-anthropic-api-key"
