/**
 * PayPerCrawl Enterprise Admin Styles
 * 
 * Professional, modern styling for the admin dashboard
 * Color scheme: Orange (#ff6b35), <PERSON> Blue (#2c3e50), <PERSON> (#4CAF50)
 * 
 * @package PayPerCrawl_Enterprise
 * @version 6.0.0
 */

/* ==========================================================================
   Base Styles & Layout
   ========================================================================== */

.paypercrawl-dashboard,
.paypercrawl-analytics,
.paypercrawl-settings,
.paypercrawl-logs {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
}

.paypercrawl-dashboard .wrap {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    margin: 20px 20px 20px 0;
    padding: 30px;
}

/* ==========================================================================
   Early Access Banner
   ========================================================================== */

.paypercrawl-early-access-banner {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    border-radius: 12px;
    margin: -30px -30px 30px -30px;
    padding: 25px 30px;
    color: white;
    position: relative;
    overflow: hidden;
}

.paypercrawl-early-access-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateX(-100px); }
    100% { transform: translateX(100px); }
}

.banner-content {
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    z-index: 1;
}

.banner-icon {
    font-size: 48px;
    line-height: 1;
}

.banner-text h3 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 700;
    color: white;
}

.banner-text p {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
    line-height: 1.5;
}

.banner-action {
    margin-left: auto;
}

.banner-action .button {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.banner-action .button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* ==========================================================================
   Header & Navigation
   ========================================================================== */

.wp-heading-inline {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 30px;
}

.wp-heading-inline .dashicons {
    color: #ff6b35;
    font-size: 36px;
}

.paypercrawl-dashboard-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.paypercrawl-dashboard-actions .button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 18px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.paypercrawl-dashboard-actions .button-primary {
    background: #ff6b35;
    border-color: #ff6b35;
}

.paypercrawl-dashboard-actions .button-primary:hover {
    background: #e55a2b;
    border-color: #e55a2b;
    transform: translateY(-1px);
}

.paypercrawl-dashboard-actions .button-secondary {
    background: #f8f9fa;
    border-color: #e9ecef;
    color: #495057;
}

.paypercrawl-dashboard-actions .button-secondary:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

/* ==========================================================================
   Metric Cards Grid
   ========================================================================== */

.paypercrawl-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.paypercrawl-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.paypercrawl-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.paypercrawl-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b35, #f7931e);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.card-icon {
    font-size: 24px;
    opacity: 0.8;
}

.card-content .metric-value {
    font-size: 36px;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
    margin-bottom: 8px;
}

.card-content .metric-label {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.4;
}

/* Card-specific colors */
.revenue-card::before {
    background: linear-gradient(90deg, #4CAF50, #45a049);
}

.detection-card::before {
    background: linear-gradient(90deg, #2196F3, #1976D2);
}

.potential-card::before {
    background: linear-gradient(90deg, #ff6b35, #f7931e);
}

.performance-card::before {
    background: linear-gradient(90deg, #9C27B0, #7B1FA2);
}

/* ==========================================================================
   Charts Section
   ========================================================================== */

.paypercrawl-charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 40px;
}

@media (max-width: 1200px) {
    .paypercrawl-charts-grid {
        grid-template-columns: 1fr;
    }
}

.paypercrawl-chart-container {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.chart-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.chart-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.chart-controls select {
    padding: 6px 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

.chart-toggle {
    padding: 6px 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: white;
    color: #495057;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-toggle.active {
    background: #ff6b35;
    color: white;
    border-color: #ff6b35;
}

.chart-content {
    height: 300px;
    position: relative;
}

.chart-content canvas {
    max-height: 100%;
}

/* ==========================================================================
   Quick Stats
   ========================================================================== */

.paypercrawl-quick-stats {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
}

.paypercrawl-quick-stats h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.stats-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.stat-value {
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
}

.stat-value.positive {
    color: #4CAF50;
}

.stat-value.negative {
    color: #f44336;
}

/* ==========================================================================
   Tables
   ========================================================================== */

.paypercrawl-recent-detections,
.paypercrawl-top-bots {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
    margin-bottom: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.section-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.detections-table-container {
    overflow-x: auto;
}

.wp-list-table {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.wp-list-table th {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 16px 12px;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.wp-list-table td {
    padding: 16px 12px;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.wp-list-table tr:hover {
    background: #f8f9fa;
}

.confidence-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    min-width: 60px;
    display: inline-block;
}

.confidence-badge.high-confidence {
    background: #d4edda;
    color: #155724;
}

.confidence-badge.medium-confidence {
    background: #fff3cd;
    color: #856404;
}

.confidence-badge.low-confidence {
    background: #f8d7da;
    color: #721c24;
}

.revenue-cell {
    color: #4CAF50;
    font-weight: 600;
}

.page-cell {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.no-detections {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px;
}

/* ==========================================================================
   Top Bots Grid
   ========================================================================== */

.top-bots-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.top-bot-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
}

.top-bot-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.bot-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.bot-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.bot-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.bot-badge:contains("Premium") {
    background: #ff6b35;
    color: white;
}

.bot-badge:contains("Standard") {
    background: #2196F3;
    color: white;
}

.bot-badge:contains("Emerging") {
    background: #4CAF50;
    color: white;
}

.bot-badge:contains("Basic") {
    background: #6c757d;
    color: white;
}

.bot-stats {
    display: flex;
    gap: 16px;
}

.bot-stats .stat {
    text-align: center;
    flex: 1;
}

.bot-stats .stat-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
}

.bot-stats .stat-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
}

/* ==========================================================================
   Modal Styles
   ========================================================================== */

.paypercrawl-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 30px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
}

.close-modal {
    font-size: 28px;
    color: #6c757d;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: #495057;
}

.modal-body {
    padding: 30px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 30px;
}

.feature-item {
    text-align: center;
    padding: 20px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.feature-icon {
    font-size: 32px;
    margin-bottom: 12px;
    display: block;
}

.feature-item h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.feature-item p {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
}

.pricing-info {
    text-align: center;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 24px;
}

.price-tier h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
}

.price {
    font-size: 36px;
    font-weight: 700;
    color: #ff6b35;
    margin-bottom: 8px;
}

.modal-footer {
    padding: 24px 30px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
    justify-content: center;
}

.modal-footer .button-large {
    padding: 12px 32px;
    font-size: 16px;
    border-radius: 8px;
}

/* ==========================================================================
   Settings Page
   ========================================================================== */

.paypercrawl-settings .settings-sections {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
}

.settings-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
}

.settings-section h3 {
    margin: 0 0 20px 0;
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.form-table th {
    width: 200px;
    font-weight: 600;
    color: #495057;
}

.form-table td {
    padding: 15px 10px 15px 0;
}

.form-table .regular-text {
    width: 400px;
    max-width: 100%;
}

.test-result {
    margin-top: 15px;
    padding: 12px;
    border-radius: 6px;
}

.test-result.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.test-result.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* ==========================================================================
   Analytics Page
   ========================================================================== */

.analytics-tabs {
    display: flex;
    gap: 0;
    margin-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}

.tab-button {
    padding: 16px 24px;
    border: none;
    background: none;
    color: #6c757d;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-button.active {
    color: #ff6b35;
    border-bottom-color: #ff6b35;
}

.tab-button:hover {
    color: #495057;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.analytics-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
    margin-bottom: 30px;
}

.analytics-section h3 {
    margin: 0 0 20px 0;
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.forecast-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
}

.forecast-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.forecast-card h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.forecast-value {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 8px;
}

.forecast-confidence,
.forecast-trend {
    font-size: 14px;
    font-weight: 500;
}

.forecast-confidence {
    color: #6c757d;
}

.forecast-trend.positive {
    color: #4CAF50;
}

.forecast-trend.negative {
    color: #f44336;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .paypercrawl-cards-grid {
        grid-template-columns: 1fr;
    }
    
    .paypercrawl-charts-grid {
        grid-template-columns: 1fr;
    }
    
    .banner-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .banner-action {
        margin-left: 0;
    }
    
    .paypercrawl-dashboard-actions {
        flex-direction: column;
    }
    
    .chart-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .modal-header {
        padding: 20px;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .modal-footer {
        padding: 20px;
        flex-direction: column;
    }
    
    .top-bots-grid {
        grid-template-columns: 1fr;
    }
    
    .bot-stats {
        gap: 8px;
    }
}

/* ==========================================================================
   Loading States & Animations
   ========================================================================== */

.paypercrawl-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
}

.paypercrawl-loading::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top-color: #ff6b35;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 12px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.paypercrawl-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.paypercrawl-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-success {
    color: #4CAF50;
}

.text-danger {
    color: #f44336;
}

.text-warning {
    color: #ff9800;
}

.text-muted {
    color: #6c757d;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: 8px;
}

.mb-2 {
    margin-bottom: 16px;
}

.mb-3 {
    margin-bottom: 24px;
}

.mt-0 {
    margin-top: 0;
}

.mt-1 {
    margin-top: 8px;
}

.mt-2 {
    margin-top: 16px;
}

.mt-3 {
    margin-top: 24px;
}

.d-none {
    display: none;
}

.d-block {
    display: block;
}

.d-flex {
    display: flex;
}

.align-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.gap-1 {
    gap: 8px;
}

.gap-2 {
    gap: 16px;
}

.gap-3 {
    gap: 24px;
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .paypercrawl-early-access-banner,
    .paypercrawl-dashboard-actions,
    .chart-controls,
    .modal {
        display: none;
    }
    
    .paypercrawl-dashboard {
        background: white;
        box-shadow: none;
    }
    
    .paypercrawl-card,
    .paypercrawl-chart-container {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}
