(()=>{var e={};e.id=395,e.ids=[395],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1678:(e,r,t)=>{"use strict";t.d(r,{db:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient({log:["query"]})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9371:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d,PATCH:()=>c});var a=t(6559),n=t(8088),i=t(7719),o=t(2190),p=t(1678);function u(e){let r=e.headers.get("authorization");return r?.replace("Bearer ","")===process.env.ADMIN_API_KEY}async function d(e){try{if(!u(e))return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),t=r.get("status"),s=parseInt(r.get("page")||"1"),a=parseInt(r.get("limit")||"10"),n=(s-1)*a,i=t?{status:t}:{},[d,c]=await Promise.all([p.db.betaApplication.findMany({where:i,orderBy:{createdAt:"desc"},skip:n,take:a}),p.db.betaApplication.count({where:i})]);return o.NextResponse.json({applications:d,pagination:{page:s,limit:a,total:c,pages:Math.ceil(c/a)}})}catch(e){return console.error("Error fetching applications:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e){try{if(!u(e))return o.NextResponse.json({error:"Unauthorized"},{status:401});let{applicationId:r,status:t,notes:s}=await e.json(),a=await p.db.betaApplication.update({where:{id:r},data:{status:t||void 0,notes:s||void 0,updatedAt:new Date}});return o.NextResponse.json(a)}catch(e){return console.error("Error updating application:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/applications/route",pathname:"/api/admin/applications",filename:"route",bundlePath:"app/api/admin/applications/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\admin\\applications\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:m}=l;function v(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(9371));module.exports=s})();