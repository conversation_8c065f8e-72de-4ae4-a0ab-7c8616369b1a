(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1949:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3389:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},3701:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\components\\theme-provider.tsx","ThemeProvider")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>u});var s=r(7413),o=r(2376),n=r.n(o),i=r(8726),a=r.n(i);r(1135);var d=r(9737),l=r(3701);let u={title:"PayPerCrawl - AI Content Monetization Platform",description:"Turn AI bot traffic into revenue. PayPerCrawl detects AI bots crawling your content and converts them into paying customers. Built for WordPress with enterprise-grade security.",keywords:["AI monetization","WordPress","content protection","bot detection","revenue generation","AI training data"],authors:[{name:"PayPerCrawl Team"}],openGraph:{title:"PayPerCrawl - AI Content Monetization Platform",description:"Turn AI bot traffic into revenue with advanced bot detection and monetization",url:"https://paypercrawl.tech",siteName:"PayPerCrawl",type:"website"},twitter:{card:"summary_large_image",title:"PayPerCrawl - AI Content Monetization Platform",description:"Turn AI bot traffic into revenue with advanced bot detection and monetization"}};function c({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsxs)("body",{className:`${n().variable} ${a().variable} antialiased bg-background text-foreground`,children:[(0,s.jsx)(l.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e}),(0,s.jsx)(d.Toaster,{})]})})}},4780:(e,t,r)=>{"use strict";r.d(t,{Y:()=>i,cn:()=>n});var s=r(9384),o=r(2348);function n(...e){return(0,o.QP)((0,s.$)(e))}function i(e){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(e)}},4947:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>b});var s=r(687),o=r(9867),n=r(3210),i=r(7313),a=r(4224),d=r(1860),l=r(4780);let u=i.Kq,c=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.LM,{ref:r,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));c.displayName=i.LM.displayName;let p=(0,a.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),m=n.forwardRef(({className:e,variant:t,...r},o)=>(0,s.jsx)(i.bL,{ref:o,className:(0,l.cn)(p({variant:t}),e),...r}));m.displayName=i.bL.displayName,n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.rc,{ref:r,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=i.rc.displayName;let f=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.bm,{ref:r,className:(0,l.cn)("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,s.jsx)(d.A,{className:"h-4 w-4"})}));f.displayName=i.bm.displayName;let v=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.hE,{ref:r,className:(0,l.cn)("text-sm font-semibold [&+div]:text-xs",e),...t}));v.displayName=i.hE.displayName;let h=n.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.VY,{ref:r,className:(0,l.cn)("text-sm opacity-90",e),...t}));function b(){let{toasts:e}=(0,o.dj)();return(0,s.jsxs)(u,{children:[e.map(function({id:e,title:t,description:r,action:o,...n}){return(0,s.jsxs)(m,{...n,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&(0,s.jsx)(v,{children:t}),r&&(0,s.jsx)(h,{children:r})]}),o,(0,s.jsx)(f,{})]},e)}),(0,s.jsx)(c,{})]})}h.displayName=i.VY.displayName},5010:(e,t,r)=>{Promise.resolve().then(r.bind(r,3701)),Promise.resolve().then(r.bind(r,9737))},5291:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=r(5239),o=r(8088),n=r(8170),i=r.n(n),a=r(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=[],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},6871:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var s=r(687);r(3210);var o=r(218);function n({children:e,...t}){return(0,s.jsx)(o.N,{...t,children:e})}},8570:(e,t,r)=>{Promise.resolve().then(r.bind(r,6871)),Promise.resolve().then(r.bind(r,4947))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9737:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\components\\ui\\toaster.tsx","Toaster")},9867:(e,t,r)=>{"use strict";r.d(t,{dj:()=>p});var s=r(3210);let o=0,n=new Map,i=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function u(e){l=a(l,e),d.forEach(e=>{e(l)})}function c({...e}){let t=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>u({type:"DISMISS_TOAST",toastId:t});return u({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=s.useState(l);return s.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,703],()=>r(5291));module.exports=s})();