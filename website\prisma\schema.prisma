generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model BetaApplication {
  id          String   @id @default(cuid())
  name        String
  email       String   @unique
  position    String
  resumeUrl   String?
  phone       String?
  website     String?
  coverLetter String?
  status      String   @default("pending")
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("beta_applications")
}

model WaitlistEntry {
  id          String    @id @default(cuid())
  email       String    @unique
  name        String
  website     String?
  companySize String?
  useCase     String?
  status      String    @default("pending")
  inviteToken String?   @unique
  invitedAt   DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("waitlist_entries")
}

model EmailLog {
  id        String   @id @default(cuid())
  to        String
  subject   String
  body      String
  status    String
  provider  String
  createdAt DateTime @default(now())

  @@map("email_logs")
}

model ContactSubmission {
  id        String   @id @default(cuid())
  name      String
  email     String
  subject   String?
  message   String
  status    String   @default("pending")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("contact_submissions")
}
