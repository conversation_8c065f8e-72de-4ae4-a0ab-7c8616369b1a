# CrawlGuard Pro Environment Configuration
# Copy this file to .env and fill in your actual values

# Cloudflare Workers Configuration
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_WORKER_ROUTE=https://your-worker.your-subdomain.workers.dev

# CrawlGuard API Configuration
CRAWLGUARD_API_KEY=cg_prod_9c4j1kwQaabRvYu2owwh6fLyGffOty1zx
CRAWLGUARD_API_URL=https://crawlguard-api-prod.crawlguard-api.workers.dev/v1
CRAWLGUARD_SITE_ID=auto_generated_on_activation

# Stripe Payment Configuration
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_CONNECT_CLIENT_ID=ca_your_connect_client_id

# Revenue Sharing Configuration (percentages)
PLATFORM_FEE_PERCENTAGE=15
PUBLISHER_REVENUE_SHARE=85

# Bot Detection ML API
ML_API_ENDPOINT=https://ml.crawlguard.com/v1/predict
ML_API_KEY=your_ml_api_key

# WebSocket Configuration for Real-time Dashboard
WEBSOCKET_SERVER_URL=wss://realtime.crawlguard.com
WEBSOCKET_API_KEY=your_websocket_api_key

# Remote Monitoring & Logging
SENTRY_DSN=https://<EMAIL>/project_id
LOGGLY_TOKEN=your_loggly_token
REMOTE_LOGGING_ENABLED=true

# Database Configuration (if using external DB for analytics)
ANALYTICS_DB_HOST=localhost
ANALYTICS_DB_NAME=crawlguard_analytics
ANALYTICS_DB_USER=analytics_user
ANALYTICS_DB_PASSWORD=analytics_password

# Redis Configuration (for caching and real-time data)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Feature Flags
ENABLE_ML_DETECTION=true
ENABLE_REAL_TIME_ANALYTICS=true
ENABLE_PAYMENT_PROCESSING=true
ENABLE_CLOUDFLARE_WORKERS=true

# Development/Production Mode
ENVIRONMENT=production
DEBUG_MODE=false
