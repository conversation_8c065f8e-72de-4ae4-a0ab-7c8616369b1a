{"name": "@arbiter/frontend-enhanced", "version": "2.0.0", "description": "Enhanced Creator and AI Company Portal", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "jest", "test:e2e": "cypress open", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.28.0", "react-query": "^3.39.3", "react-hook-form": "^7.53.0", "react-dropzone": "^14.2.9", "@hookform/resolvers": "^3.9.0", "yup": "^1.4.0", "axios": "^1.7.7", "socket.io-client": "^4.8.0", "chart.js": "^4.4.4", "react-chartjs-2": "^5.2.0", "date-fns": "^4.1.0", "react-select": "^5.8.1", "react-table": "^7.8.0", "react-hot-toast": "^2.4.1", "framer-motion": "^11.9.0", "lucide-react": "^0.446.0", "@headlessui/react": "^2.1.9", "@heroicons/react": "^2.1.5", "clsx": "^2.1.1", "tailwind-merge": "^2.5.2"}, "devDependencies": {"@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "vite": "^5.4.8", "typescript": "^5.6.2", "tailwindcss": "^3.4.13", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "@types/node": "^22.7.4", "eslint": "^9.11.1", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "jest": "^29.7.0", "@types/jest": "^29.5.13", "cypress": "^13.15.0"}}