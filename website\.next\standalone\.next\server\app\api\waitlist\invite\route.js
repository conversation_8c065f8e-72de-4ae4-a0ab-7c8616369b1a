(()=>{var e={};e.id=163,e.ids=[163],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5069:(e,t,r)=>{"use strict";r.d(t,{db:()=>n});var s=r(6330);let n=globalThis.prisma??new s.PrismaClient({log:["query"]})},6330:e=>{"use strict";e.exports=require("@prisma/client")},6487:()=>{},6827:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>x,serverHooks:()=>y,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>v,POST:()=>c});var n=r(6559),i=r(8088),a=r(7719),o=r(2190),u=r(5069),d=r(639),p=r(4250);let l=d.Ik({email:d.Yj().email("Invalid email address"),adminKey:d.Yj().min(1,"Admin key required")});async function c(e){try{let t=await e.json(),{email:r,adminKey:s}=l.parse(t);if(s!==process.env.ADMIN_API_KEY)return o.NextResponse.json({error:"Unauthorized"},{status:401});let n=await u.db.waitlistEntry.findUnique({where:{email:r}});if(!n)return o.NextResponse.json({error:"Email not found on waitlist"},{status:404});if("invited"===n.status)return o.NextResponse.json({error:"User already invited"},{status:400});let i=await u.db.waitlistEntry.update({where:{email:r},data:{status:"invited",invitedAt:new Date}});return o.NextResponse.json({message:"Beta invite sent successfully",entry:i})}catch(e){if(console.error("Invite error:",e),e instanceof p.G)return o.NextResponse.json({error:"Validation failed",details:e.errors},{status:400});return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function v(e){try{let{searchParams:t}=new URL(e.url);if(t.get("adminKey")!==process.env.ADMIN_API_KEY)return o.NextResponse.json({error:"Unauthorized"},{status:401});let r=await u.db.waitlistEntry.findMany({where:{status:"invited"},orderBy:{invitedAt:"desc"}});return o.NextResponse.json(r)}catch(e){return console.error("Error fetching invited users:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/waitlist/invite/route",pathname:"/api/waitlist/invite",filename:"route",bundlePath:"app/api/waitlist/invite/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\waitlist\\invite\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:m,serverHooks:y}=x;function f(){return(0,a.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:m})}},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,639],()=>r(6827));module.exports=s})();