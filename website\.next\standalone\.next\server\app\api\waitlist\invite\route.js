(()=>{var e={};e.id=163,e.ids=[163],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1111:(e,t,r)=>{"use strict";r.d(t,{F7:()=>l,G1:()=>n,M:()=>d,YL:()=>p});var i=r(549),a=r(1678);let o=new i.u(process.env.RESEND_API_KEY);async function s({to:e,subject:t,html:r,from:i="PayPerCrawl <<EMAIL>>"}){try{let{data:s,error:n}=await o.emails.send({from:i,to:e,subject:t,html:r});if(await a.db.emailLog.create({data:{to:e,subject:t,body:r,status:n?"failed":"sent",provider:"resend"}}),n)throw console.error("Email send error:",n),console.error("Error details:",JSON.stringify(n,null,2)),Error(`Failed to send email: ${n.message||JSON.stringify(n)}`);return s}catch(i){throw console.error("Email service error:",i),await a.db.emailLog.create({data:{to:e,subject:t,body:r,status:"failed",provider:"resend"}}),i}}async function n(e,t){return s({to:e,subject:"Application Received - PayPerCrawl Beta",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Application Received</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2563eb;">Thank you for your application!</h1>
        
        <p>Hi ${t},</p>
        
        <p>We've received your application for the PayPerCrawl beta program. Our team will review your application and get back to you within 2-3 business days.</p>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">What's Next?</h3>
          <ul>
            <li>Our team will review your application</li>
            <li>We'll contact you if we need additional information</li>
            <li>Selected candidates will receive beta access instructions</li>
          </ul>
        </div>
        
        <p>In the meantime, feel free to explore our website and learn more about how PayPerCrawl can help monetize your AI content.</p>
        
        <p>Best regards,<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function l(e,t,r){return s({to:e,subject:"Welcome to PayPerCrawl Waitlist!",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Waitlist Confirmation</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2563eb;">You're on the waitlist! 🎉</h1>
        
        <p>Hi ${t},</p>
        
        <p>Thank you for joining the PayPerCrawl waitlist! You're currently <strong>#${r}</strong> in line.</p>
        
        <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb;">
          <h3 style="margin-top: 0; color: #2563eb;">Your Position: #${r}</h3>
          <p style="margin-bottom: 0;">We'll notify you as soon as beta access becomes available!</p>
        </div>
        
        <p>While you wait, here's what you can do:</p>
        <ul>
          <li>Follow us on social media for updates</li>
          <li>Share PayPerCrawl with friends who create AI content</li>
          <li>Read our blog for AI monetization insights</li>
        </ul>
        
        <p>We're working hard to launch the beta and can't wait to help you monetize your AI content!</p>
        
        <p>Best regards,<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function p(e,t,r){let i=`https://paypercrawl.tech/?invited=true&token=${r}`;return s({to:e,subject:"Your PayPerCrawl Beta Access is Ready! \uD83D\uDE80",html:`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Beta Access Ready</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #16a34a;">Welcome to PayPerCrawl Beta! 🚀</h1>
        
        <p>Hi ${t},</p>
        
        <p>Congratulations! You've been selected for the PayPerCrawl beta program. You can now start monetizing your AI content!</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${i}" style="background: #2563eb; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
            Access Beta Dashboard
          </a>
        </div>
        
        <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #16a34a;">
          <h3 style="margin-top: 0; color: #16a34a;">Getting Started</h3>
          <ol>
            <li>Click the button above to access your beta dashboard</li>
            <li>Install the PayPerCrawl WordPress plugin</li>
            <li>Configure your monetization settings</li>
            <li>Start earning from AI bot traffic!</li>
          </ol>
        </div>
        
        <p>Need help? Our support team is standing by to assist you with setup and configuration.</p>
        
        <p>Welcome aboard!<br>The PayPerCrawl Team</p>
        
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
        <p style="font-size: 12px; color: #6b7280;">
          PayPerCrawl - AI Content Monetization Platform<br>
          <a href="https://paypercrawl.tech">paypercrawl.tech</a>
        </p>
      </div>
    </body>
    </html>
  `})}async function d(e,t,r,i){let a=`New Contact Form Submission: ${r}`,o=`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Contact Form Submission</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #dc2626;">New Contact Form Submission</h1>
        
        <div style="background: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Name:</strong> ${e}</p>
          <p><strong>Email:</strong> ${t}</p>
          <p><strong>Subject:</strong> ${r}</p>
          <p><strong>Message:</strong></p>
          <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
            ${i.replace(/\n/g,"<br>")}
          </div>
        </div>
        
        <p>Please respond to this inquiry promptly.</p>
      </div>
    </body>
    </html>
  `;return s({to:process.env.ADMIN_EMAIL||"<EMAIL>",subject:a,html:o})}},1678:(e,t,r)=>{"use strict";r.d(t,{db:()=>a});let i=require("@prisma/client"),a=globalThis.prisma??new i.PrismaClient({log:["query"]})},2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3505:e=>{"use strict";e.exports=import("prettier/standalone")},4297:e=>{"use strict";e.exports=require("async_hooks")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5511:e=>{"use strict";e.exports=require("crypto")},6487:()=>{},6827:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{GET:()=>m,POST:()=>h});var a=r(6559),o=r(8088),s=r(7719),n=r(2190),l=r(1678),p=r(1111),d=r(639),u=r(4250),c=r(5511);let y=d.Ik({email:d.Yj().email("Invalid email address"),adminKey:d.Yj().min(1,"Admin key required")});async function h(e){try{let t=await e.json(),{email:r,adminKey:i}=y.parse(t);if(i!==process.env.ADMIN_API_KEY)return n.NextResponse.json({error:"Unauthorized"},{status:401});let a=await l.db.waitlistEntry.findUnique({where:{email:r}});if(!a)return n.NextResponse.json({error:"Email not found on waitlist"},{status:404});if("invited"===a.status)return n.NextResponse.json({error:"User already invited"},{status:400});let o=a.inviteToken;o||(o=(0,c.randomBytes)(32).toString("hex"));let s=await l.db.waitlistEntry.update({where:{email:r},data:{status:"invited",inviteToken:o,invitedAt:new Date}});try{console.log(`Attempting to send beta invite email to ${r}`),await (0,p.YL)(r,a.name,o),console.log(`Successfully sent beta invite email to ${r}`)}catch(e){console.error("Failed to send beta invite email:",e),console.error("Email error details:",JSON.stringify(e,null,2))}return n.NextResponse.json({message:"Beta invite sent successfully",entry:s})}catch(e){if(console.error("Invite error:",e),e instanceof u.G)return n.NextResponse.json({error:"Validation failed",details:e.issues},{status:400});return n.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e){try{let{searchParams:t}=new URL(e.url);if(t.get("adminKey")!==process.env.ADMIN_API_KEY)return n.NextResponse.json({error:"Unauthorized"},{status:401});let r=await l.db.waitlistEntry.findMany({where:{status:"invited"},orderBy:{invitedAt:"desc"}});return n.NextResponse.json(r)}catch(e){return console.error("Error fetching invited users:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/waitlist/invite/route",pathname:"/api/waitlist/invite",filename:"route",bundlePath:"app/api/waitlist/invite/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\plugin\\website\\src\\app\\api\\waitlist\\invite\\route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:b,workUnitAsyncStorage:f,serverHooks:w}=g;function x(){return(0,s.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:f})}},7075:e=>{"use strict";e.exports=require("node:stream")},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},8354:e=>{"use strict";e.exports=require("util")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,580,987],()=>r(6827));module.exports=i})();